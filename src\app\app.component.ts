import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { SwUpdate, VersionReadyEvent } from '@angular/service-worker';
import { DeviceDetectorService } from 'ngx-device-detector';
import { ConfirmationService, MessageService } from 'primeng/api';
import { filter, map } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Constants } from './shared/constants/app.constants';
import { DeviceInfoModel } from './shared/models/device-info.model';
import { SeoService } from './shared/services';
import { LocalStorageService, StorageItem } from './shared/services/local-storage.service';
import { SplashScreenService } from './shared/services/splash-screen.service';
import { THEMES, ThemeService } from './shared/services/theme.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { IOSDebugService } from './shared/services/ios-debug.service';
import { IOSTestService } from './shared/services/ios-test.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnInit {
  showSwUpdateDialog: boolean = false;
  deviceInfoModel: DeviceInfoModel = new DeviceInfoModel();

  constructor(
    private readonly themeService: ThemeService,
    private readonly swUpdate: SwUpdate,
    private readonly confirmationService: ConfirmationService,
    private readonly messageService: MessageService,
    private readonly splashScreenService: SplashScreenService,
    private readonly router: Router,
    private readonly seoService: SeoService,
    private readonly deviceService: DeviceDetectorService,
    private readonly cdr: ChangeDetectorRef,
    private readonly localStorageStorageService: LocalStorageService,
    private readonly snackBar: MatSnackBar,
    private readonly iosDebugService: IOSDebugService,
    private readonly iosTestService: IOSTestService
  ) {}

  ngOnInit(): void {
    this.setTheme();
    this.checkVersionUpdate();
    this.initializeRouterEventListener();
    this.runGlobalServices();
    this.getDeviceInfo();
    this.setupIOSDebugging();
  }

  private setupIOSDebugging(): void {
    const iosInfo = this.iosDebugService.getIOSInfo();
    if (iosInfo) {
      console.log('iOS device detected in AppComponent');
      console.log('iOS Info:', iosInfo);

      // Run iOS tests after a delay to ensure app is fully loaded
      setTimeout(() => {
        this.runIOSTests();
      }, 3000);
    }
  }

  private async runIOSTests(): Promise<void> {
    try {
      console.log('Running iOS compatibility tests...');
      await this.iosTestService.runIOSTests();

      // Add keyboard shortcut to show test results (Cmd+Shift+T on iOS)
      document.addEventListener('keydown', (event) => {
        if (event.metaKey && event.shiftKey && event.key === 'T') {
          this.iosTestService.displayTestResults();
        }
      });

      console.log('iOS tests completed. Press Cmd+Shift+T to view results or check console.');
    } catch (error) {
      console.error('Error running iOS tests:', error);
    }
  }

  setTheme(): void {
    const storedTheme = this.localStorageStorageService.getItem(StorageItem.Theme) as THEMES;
    this.themeService.changeTheme(storedTheme);
  }

  checkVersionUpdate(): void {
    if (environment.swUpdate) {
      if (environment.swUpdateFooter) {
        this.swUpdateDialog();
        return;
      }
      this.swUpdateConformationPopUp();
    }
  }

  swUpdateConformationPopUp(): void {
    const updatesAvailable = this.swUpdate.versionUpdates.pipe(
      filter((evt): evt is VersionReadyEvent => evt.type === 'VERSION_READY'),
      map((evt) => ({
        type: 'UPDATE_AVAILABLE',
        current: evt.currentVersion,
        available: evt.latestVersion
      }))
    );
    updatesAvailable.subscribe((evt: any) => {
      this.confirmationService.confirm({
        message: 'A new version is available. Load the new version?',
        header: 'Confirmation',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          window.location.reload();
          this.messageService.add({
            severity: 'info',
            summary: 'Confirmed',
            detail: 'You have accepted'
          });
        },
        reject: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: 'You have rejected'
          });
        }
      });
    });
  }

  swUpdateDialog(): void {
    const updatesAvailable = this.swUpdate.versionUpdates.pipe(
      filter((evt): evt is VersionReadyEvent => evt.type === 'VERSION_READY'),
      map((evt) => ({
        type: 'UPDATE_AVAILABLE',
        current: evt.currentVersion,
        available: evt.latestVersion
      }))
    );
    updatesAvailable.subscribe((evt: any) => {
      this.showSwUpdateDialog = true;
      const snackBarRef =   this.snackBar.open('A new version is available. Load the new version?', 'Update', {
        panelClass: ['sw-update-snackbar-color', 'snackbar'],
        horizontalPosition: 'left',
      });
      snackBarRef.onAction().subscribe(() => {
        this.closeSwDialog();
      });
    });
  }

  closeSwDialog(): void {
    this.showSwUpdateDialog = false;
    this.snackBar.dismiss()
    window.location.reload();
  }

  initializeRouterEventListener() {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    this.router.events.subscribe((event) => {
      if (isIOS) {
        console.log('iOS Router event:', event.constructor.name, event);
      }

      if (event instanceof NavigationEnd) {
        if (isIOS) {
          console.log('iOS Navigation completed to:', event.url);
        }

        // hide splash screen
        this.splashScreenService.hide();

        // scroll to top on every route change
        window.scrollTo(0, 0);

        // to display back the body content
        setTimeout(() => {
          document.body.classList.add('page-loaded');
        }, Constants.splashScreenTimeout);
      }

      // Handle navigation errors on iOS
      if (event.constructor.name === 'NavigationError') {
        if (isIOS) {
          console.error('iOS Navigation error:', event);
        }
      }

      // Handle navigation cancellation on iOS
      if (event.constructor.name === 'NavigationCancel') {
        if (isIOS) {
          console.warn('iOS Navigation cancelled:', event);
        }
      }
    });
  }

  private runGlobalServices(): void {
    this.seoService.init();
  }

  getDeviceInfo(): void {
    this.deviceInfoModel.deviceInfo = this.deviceService.getDeviceInfo();
    this.deviceInfoModel.isMobile = this.deviceService.isMobile(); // mobile-screen
    this.deviceInfoModel.isTablet = this.deviceService.isTablet(); // tablet-screen
    this.deviceInfoModel.isDesktop = this.deviceService.isDesktop(); // desktop-screen
    this.cdr.detectChanges();
  }
}
