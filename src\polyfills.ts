/***************************************************************************************************
 * Zone JS
 */
import 'zone.js'; // Required by Angular

/***************************************************************************************************
 * Browser polyfills (Safari & iOS)
 */
import 'core-js/es/array';
import 'core-js/es/object';
import 'core-js/es/promise';
import 'core-js/es/string';
import 'core-js/es/map';
import 'core-js/es/set';
import 'core-js/es/weak-map';
import 'core-js/es/symbol';

// Additional iOS-specific polyfills
import 'core-js/es/array/from';
import 'core-js/es/array/includes';
import 'core-js/es/array/find';
import 'core-js/es/array/find-index';
import 'core-js/es/object/assign';
import 'core-js/es/object/keys';
import 'core-js/es/object/values';
import 'core-js/es/object/entries';
import 'core-js/es/string/includes';
import 'core-js/es/string/starts-with';
import 'core-js/es/string/ends-with';
import 'core-js/es/string/repeat';
import 'core-js/es/string/pad-start';
import 'core-js/es/string/pad-end';

// ES6+ features for iOS Safari
import 'core-js/es/promise/finally';
import 'core-js/es/symbol/async-iterator';
import 'core-js/es/symbol/iterator';

/***************************************************************************************************
 * Safari fetch bug fix
 */
// import 'whatwg-fetch'; // Uncomment if needed for older iOS versions

/***************************************************************************************************
 * iOS-specific fixes
 */

// Fix for iOS Safari localStorage issues
if (typeof Storage !== 'undefined') {
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
  } catch (e) {
    // Fallback for private browsing mode
    const storage: { [key: string]: string } = {};
    (window as any).localStorage = {
      getItem: (key: string) => storage[key] || null,
      setItem: (key: string, value: string) => storage[key] = value,
      removeItem: (key: string) => delete storage[key],
      clear: () => Object.keys(storage).forEach(key => delete storage[key]),
      length: Object.keys(storage).length,
      key: (index: number) => Object.keys(storage)[index] || null
    };
  }
}

// Fix for iOS Safari viewport issues
if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
  // Prevent zoom on input focus
  const viewport = document.querySelector('meta[name=viewport]');
  if (viewport) {
    viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no');
  }

  // Fix for iOS Safari 100vh issue
  const setVH = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };
  setVH();
  window.addEventListener('resize', setVH);
  window.addEventListener('orientationchange', setVH);
}
