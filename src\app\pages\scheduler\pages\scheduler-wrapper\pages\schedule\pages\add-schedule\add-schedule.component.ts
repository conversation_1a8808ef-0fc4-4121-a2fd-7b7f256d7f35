import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import {
  AddScheduleFormGroup,
  ClassTypes,
  SuggestedTimeSlot,
  InstructorAvaibility,
  StudentPlans,
  LessonTypes,
  AssignedPlanStatus
} from '../../models';
import { DependentService } from 'src/app/pages/profile/services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { InstrumentsService } from 'src/app/request-information/services';
import { Instrument } from 'src/app/request-information/models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { SchedulerService, StudentPlanService } from '../../services';
import { MatIconModule } from '@angular/material/icon';
import { FilterPipe } from 'src/app/shared/pipe';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AppToasterService, CommonService, EcommerceItem, GoogleAnalyticsService } from 'src/app/shared/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services/plan-summary.service';
import { RevenueCategory } from 'src/app/pages/settings/pages/revenue-categories/models';
import { StudentGradeService } from 'src/app/pages/members/pages/students/services';
import { DependentInformations, StudentGrades } from 'src/app/pages/members/pages/students/models';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSidenavModule } from '@angular/material/sidenav';
import { AccountManagerSchedulePaymentComponent } from 'src/app/pages/members/pages/students/pages/account-manager-schedule-payment/account-manager-schedule-payment.component';
import { CardDetailsResponse, PaymentParams, RePaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { Debounce } from 'src/app/shared/decorators';
import { Account } from 'src/app/auth/models/user.model';
import { AuthService } from 'src/app/auth/services';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import moment from 'moment';
import { LocationService } from 'src/app/pages/room-and-location-management/pages/location/services';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    ReactiveFormsModule,
    MatIconModule,
    FormsModule,
    SharedModule,
    MatCheckboxModule,
    MatSidenavModule
  ],
  COMPONENTS: [AccountManagerSchedulePaymentComponent],
  PIPES: [FilterPipe]
};

@Component({
  selector: 'app-add-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './add-schedule.component.html',
  styleUrl: './add-schedule.component.scss'
})
export class AddScheduleComponent extends BaseComponent implements OnInit {
  @Input() isIntroductoryLesson!: boolean;
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() selectedStudentId!: number | undefined;
  @Input() accManagerDetails!: Account | null;

  addScheduleForm!: FormGroup<AddScheduleFormGroup>;
  studentList!: Array<IdNameModel>;
  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;
  instructors!: Array<Instructor> | undefined;
  studentPlans!: Array<StudentPlans>;
  revenueCategories!: Array<RevenueCategory>;
  studentGrades!: Array<StudentGrades>;
  rePaymentParams!: RePaymentParams | null;

  searchTerm!: string;
  lessonType = ClassTypes;
  maxDate = new Date();
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  selectedTimeSlot!: string | null;
  selectedStudentPlan!: StudentPlans | undefined;
  isPaymentSideNavOpen = false;
  selectedStudentAgeGroup!: number | undefined;
  showInstructorLoader = false;
  isAddressIncomplete = false;
  hideSkillType = false;
  selectedInstrumentName!: string;

  @ViewChild(AccountManagerSchedulePaymentComponent)
  accountManagerSchedulePaymentComponent!: AccountManagerSchedulePaymentComponent;

  @Output() closeModal = new EventEmitter<void>();
  @Output() refreshScheduleData = new EventEmitter<void>();
  @Output() closeViewStudent = new EventEmitter<void>();

  constructor(
    private readonly dependentService: DependentService,
    private readonly instrumentsService: InstrumentsService,
    private readonly schedulerService: SchedulerService,
    private readonly studentPlanService: StudentPlanService,
    private readonly locationService: LocationService,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef,
    private readonly commonService: CommonService,
    private readonly studentGradeService: StudentGradeService,
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly googleAnalyticsService: GoogleAnalyticsService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initAddScheduleForm();
    this.getStudents();
    this.getRevenueCategories();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectedStudentId']?.currentValue) {
      this.selectedStudentId = changes['selectedStudentId']?.currentValue;
      this.getStudentGrades(this.selectedStudentId);
    }
  }

  initAddScheduleForm(): void {
    this.addScheduleForm = new FormGroup<AddScheduleFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      classType: new FormControl(this.isIntroductoryLesson ? this.lessonType.INTRODUCTORY : undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      studentId: new FormControl(this.selectedStudentId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      lessonType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      skillType: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      instrumentId: new FormControl(undefined, { nonNullable: true }),
      locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      instructorId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      scheduleDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      daysOfSchedule: new FormArray([] as FormControl<number>[]),
      scheduleStartTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      planId: new FormControl(undefined, { nonNullable: true }),
      isSpecialNeedsLesson: new FormControl(false, { nonNullable: true }),
      isAllInstances: new FormControl(false, { nonNullable: true }),
      revenueCategoryId: new FormControl(undefined, { nonNullable: true }),
      roomId: new FormControl(undefined, { nonNullable: true }),
      isDraftSchedule: new FormControl(false, { nonNullable: true })
    });
  }

  getFilterParamsForStudent(): void {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      InstructorFilter: this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR ? [this.currentUser?.dependentId] : [],
      locationFilter: []
    });
  }

  getStudents(): void {
    this.dependentService
      .add(this.getFilterParamsForStudent(), API_URL.dependentInformations.getAllStudents)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<DependentInformations[]>) => {
          this.studentList = res.result.map(item => ({
            id: item.id,
            dependentId: item.accountManagerId,
            name: `${item.firstName} ${item.lastName}`,
            age: item.age
          }));
          this.setAgeGroupFromAge(this.studentList.find(student => student.id === this.selectedStudentId)?.age!);
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(ageGroup?: number): void {
    if (!ageGroup) {
      return;
    }
    this.setFormControlValue('instrumentId', undefined);
    this.instrumentsService
      .getList<CBResponse<Instrument>>(`${API_URL.crud.getAll}?AgeGroup=${ageGroup}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    const body =
      this.addScheduleForm.controls.classType.value === ClassTypes.INTRODUCTORY
        ? { dayOffDate: this.addScheduleForm.controls.scheduleDate.value }
        : {};
    this.locationService
      .add(body, API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  @Debounce(500)
  getInstructors(): void {
    this.showInstructorLoader = true;
    if (
      this.getInstructorAvailability.scheduleStartDate &&
      this.getInstructorAvailability.locationId &&
      this.getInstructorAvailability.instrumentId &&
      (this.getInstructorAvailability.classType !== this.lessonType.RECURRING ||
        (this.getInstructorAvailability.planId && this.getInstructorAvailability.daysOfSchedule?.length))
    ) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getAllInstructor)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.addScheduleForm.controls.instructorId.reset();
            this.showInstructorLoader = false;
            this.cdr.detectChanges();
          },
          error: () => {
            this.showInstructorLoader = false;
            this.cdr.detectChanges();
          }
        });
    }
  }

  getStudentPlans(studentId: number): void {
    if (this.addScheduleForm.getRawValue().classType === ClassTypes.RECURRING) {
      this.studentPlanService
        .getList<CBResponse<StudentPlans>>(`${API_URL.studentPlans.getStudentPlans}?DependentInformationId=${studentId}&isSchedule=true`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: CBResponse<StudentPlans>) => {
            this.studentPlans = res.result.items.filter(
              plan => !plan.isRentalPlan && plan.assignedPlanStatus === AssignedPlanStatus.ASSIGNED_PLAN
            );
            this.addScheduleForm.controls.planId.reset();
            this.cdr.detectChanges();
          },
          error: () => {
            this.showPageLoader = false;
            this.cdr.detectChanges();
          }
        });
    }
  }

  getStudentGrades(studentId?: number): void {
    if (!studentId) {
      return;
    }
    this.studentGradeService
      .getList<CBResponse<StudentGrades>>(`${API_URL.studentGrades.getAllByStudentId}?studentId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentGrades>) => {
          this.studentGrades = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: number | string | boolean | undefined): void {
    (this.addScheduleForm.controls as any)[controlName].setValue(value);
    if (['instrumentId', 'skillType'].includes(controlName)) {
      this.getInstructors();
      this.setSkillBasedOnGrade();
    }
  }

  hideSkillTypeFn(): void {
    const instrumentIds = this.instruments
      ?.filter(instrument => instrument.instrumentDetail.name === 'General Music' || instrument.instrumentDetail.name === 'Early Starters')
      .map(instrument => instrument.instrumentDetail.id);
    const selectedInstrumentId = this.addScheduleForm?.get('instrumentId')?.value ?? 0;

    this.hideSkillType = instrumentIds?.includes(selectedInstrumentId);
    if (this.hideSkillType) {
      this.setRequiredBasedOnCondition('skillType', false);
      this.setFormControlValue('skillType', '');
    }
  }

  getMatchingGrade(): StudentGrades | undefined {
    return this.studentGrades?.find(
      studentGrade =>
        studentGrade.studentGrade.instrumentId ===
        (this.addScheduleForm.getRawValue().instrumentId ?? this.selectedStudentPlan?.studentplan.instrumentDetailId)
    );
  }

  setSkillBasedOnGrade(): void {
    if (this.getMatchingGrade()) {
      this.addScheduleForm.controls.skillType.setValue(this.getSkillBasedOnGrade(this.getMatchingGrade()?.studentGrade.grade!));
    }
    this.cdr.detectChanges();
  }

  setLowestGradeBasedOnSkillType(skillType: string): number {
    const skillRange = this.constants.skillGradeRanges.find(range => range.skillType === skillType);
    return skillRange ? skillRange.minGrade : 0;
  }

  getSkillBasedOnGrade(grade: number): string {
    const skillRange = this.constants.skillGradeRanges.find(range => grade >= range.minGrade && grade <= range.maxGrade);
    return skillRange ? skillRange.skillType : '';
  }

  setStudentGrade(): void {
    this.studentGradeService
      .add(
        {
          instrumentId: this.addScheduleForm.getRawValue().instrumentId ?? this.selectedStudentPlan?.studentplan.instrumentDetailId,
          studentId: this.addScheduleForm.getRawValue().studentId,
          grade: this.setLowestGradeBasedOnSkillType(this.addScheduleForm.getRawValue().skillType)
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setAgeGroupFromAge(studentAge: number): void {
    this.selectedStudentAgeGroup = undefined;
    switch (true) {
      case studentAge <= 5:
        this.selectedStudentAgeGroup = 1;
        break;
      case studentAge > 5 && studentAge <= 8:
        this.selectedStudentAgeGroup = 2;
        break;
      case studentAge > 8 && studentAge <= 17:
        this.selectedStudentAgeGroup = 3;
        break;
      case studentAge > 17:
        this.selectedStudentAgeGroup = 4;
        break;
    }

    this.getInstruments(this.selectedStudentAgeGroup);
  }

  onClassTypeChange(classType: number | undefined): void {
    this.resetAddScheduleForm();
    this.setFormControlValue('classType', classType ?? '');
    const isRecurringClass = classType === ClassTypes.RECURRING;
    this.setFormControlValue('isAllInstances', isRecurringClass);
    this.setRequiredBasedOnCondition('planId', isRecurringClass);
    this.setRequiredBasedOnCondition('daysOfSchedule', isRecurringClass);
    this.setRequiredBasedOnCondition('instrumentId', !isRecurringClass);
  }

  onLessonTypeChange(lessonType: number | undefined): void {
    const categoryName =
      lessonType === LessonTypes.IN_PERSON
        ? this.constants.revenueCategories.inPersonRevenue
        : this.constants.revenueCategories.remoteRevenue;

    const id = this.revenueCategories?.find(item => item.category.categoryName === categoryName)?.category.id!;
    this.setFormControlValue('revenueCategoryId', id);
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.addScheduleForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  getDayOfWeek(): void {
    const days = CommonUtils.getDaysOfWeekBetween(
      this.addScheduleForm.controls.scheduleDate.value,
      this.addScheduleForm.controls.scheduleDate.value
    );
    const daysOfSchedule = this.addScheduleForm.get('daysOfSchedule') as FormArray;
    daysOfSchedule.clear();
    days.forEach(day => daysOfSchedule.push(new FormControl(day, { nonNullable: true })));
  }

  setDaysOfWeek(dayValue: number): void {
    const daysOfSchedule = this.addScheduleForm.get('daysOfSchedule') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);

    if (index !== -1) {
      daysOfSchedule.removeAt(index);
    } else {
      if (daysOfSchedule.length < this.selectedStudentPlan?.planDetails[0].planDetail.visitsPerWeek!) {
        daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
      } else {
        this.toasterService.error(this.constants.errorMessages.maxDaysSelected);
        return;
      }
    }
    setTimeout(() => {
      this.getSuggestedTimeAndInstructors(false);
    }, 0);
  }

  isDaySelected(dayValue: number): boolean | undefined {
    const daysOfSchedule = this.addScheduleForm.get('daysOfSchedule')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  getSuggestedTimeAndInstructors(resetDaysOfSchedule: boolean): void {
    if (resetDaysOfSchedule) {
      this.selectedStudentPlan = this.studentPlans.find(plan => plan.studentplan.id === this.addScheduleForm.get('planId')?.value);
      this.setSkillBasedOnGrade();
      this.addScheduleForm.controls.daysOfSchedule.clear();
    }
    this.getInstructors();
    this.getSuggestedTime();
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.addScheduleForm.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime,
      roomId: selectedTimeSlot.roomId
    });
  }

  get getInstructorAvailability(): InstructorAvaibility {
    return {
      classType: this.addScheduleForm.controls.classType.value,
      instructorId: this.addScheduleForm.controls.instructorId.value,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.addScheduleForm.controls.scheduleDate.value).startUtc,
      scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.addScheduleForm.controls.scheduleDate.value).endUtc,
      daysOfSchedule: this.addScheduleForm.controls.daysOfSchedule.value,
      isAllInstances: this.addScheduleForm.controls.isAllInstances.value,
      locationId: this.addScheduleForm.controls.locationId.value,
      isIntroductoryAvaLable: this.addScheduleForm.controls.classType.value === ClassTypes.INTRODUCTORY,
      planId: this.addScheduleForm.controls.planId.value ?? null,
      duration: this.selectedStudentPlan?.planDetails[0].planDetail.duration ?? null,
      instrumentId: this.addScheduleForm.controls.instrumentId.value ?? this.selectedStudentPlan?.studentplan.instrumentDetailId,
      skillType: this.addScheduleForm.controls.skillType.value,
      studentId: this.addScheduleForm.controls.studentId.value
    };
  }

  getSuggestedTime(): void {
    const availability = this.getInstructorAvailability;
    if (availability.classType && availability.instructorId && availability.scheduleStartDate) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getInstructorAvaibility)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
            const scheduleDate = this.addScheduleForm.controls.scheduleDate.value;
            const isToday = moment(scheduleDate).isSame(moment(), 'day');
            this.suggestedTimeSlots = [];
            this.suggestedTimeSlots = response.result
              .map(timeSlot => {
                return {
                  ...timeSlot,
                  startTime: DateUtils.toLocal(timeSlot.startTime),
                  endTime: DateUtils.toLocal(timeSlot.endTime)
                };
              })
              .filter(timeSlot => {
                if (this.addScheduleForm.controls.classType.value !== ClassTypes.INTRODUCTORY || !isToday) {
                  return true;
                }
                return CommonUtils.isFutureLesson(timeSlot.startTime);
              });
            this.selectedTimeSlot = null;
            this.addScheduleForm.controls.scheduleStartTime.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
      this.isAddressIncomplete = isIncomplete;
      this.cdr.markForCheck();
    });
  }

  onSchedulerFormSubmit(cardDetails = null): void {
    this.paymentService.setUserPayment(null);

    if (this.addScheduleForm.invalid) {
      this.addScheduleForm.markAllAsTouched();
      return;
    }
    this.addScheduleForm.markAsUntouched();
    this.showBtnLoader = true;
    if (this.addScheduleForm.controls.classType.value === ClassTypes.INTRODUCTORY) {
      this.setGoogleAnalyticsCheckoutEvent();
    }
    if (this.addScheduleForm.getRawValue().id) {
      this.onUpdateSchedule();
    } else {
      this.onAddSchedule();
    }
  }

  get getEcommerceItem() {
    return {
      name: this.getClassName(this.addScheduleForm.controls.classType.value || 1),
      instrumentName: this.selectedInstrumentName || this.selectedStudentPlan?.instrumentName,
      locationName: this.getLocationNameFromValue(this.addScheduleForm.controls.locationId.value),
      instructorId: this.addScheduleForm.controls.instructorId.value,
      scheduleStartTime: this.addScheduleForm.controls.scheduleStartTime.value,
      scheduleEndTime: this.addScheduleForm.controls.scheduleEndTime.value,
      discount: +this.accountManagerSchedulePaymentComponent?.discountAmount || 0,
      quantity: 1,
      price:
        this.addScheduleForm.controls.classType.value === ClassTypes.INTRODUCTORY
          ? this.constants.defaultAmountPayableForIntroductoryClasses
          : this.selectedStudentPlan?.planPrice || 0
    };
  }

  setGoogleAnalyticsCheckoutEvent(): void {
    this.googleAnalyticsService.trackBeginCheckout({
      currency: 'USD',
      value: this.getEcommerceItem.price,
      items: [this.googleAnalyticsService.createEcommerceItem(this.getEcommerceItem)]
    });
  }

  setGoogleAnalyticsPurchaseEvent(): void {
    this.googleAnalyticsService.trackPurchase({
      currency: 'USD',
      value: this.getEcommerceItem.price,
      items: [this.googleAnalyticsService.createEcommerceItem(this.getEcommerceItem)]
    });
  }

  onAddSchedule(): void {
    this.schedulerService
      .add(this.getAddScheduleParams(), API_URL.referralDetails.create)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<{ id: number; studentId: number }>) => {
          this.setFormControlValue('id', res.result?.id);
          this.setFormControlValue('studentId', res.result?.studentId);
          this.refreshScheduleData.emit();
          if (!this.getMatchingGrade()) {
            this.setStudentGrade();
          }
          if (this.addScheduleForm.getRawValue().classType === ClassTypes.INTRODUCTORY) {
            this.isPaymentSideNavOpen = true;
          } else {
            this.resetAddScheduleForm();
            this.closeModal.emit();
          }
          this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Schedule'));
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onUpdateSchedule(): void {
    this.schedulerService
      .update({ ...this.getAddScheduleParams(), instructorId: [this.addScheduleForm.getRawValue().instructorId] }, API_URL.crud.update)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.refreshScheduleData.emit();
          if (!this.getMatchingGrade()) {
            this.setStudentGrade();
          }
          if (this.addScheduleForm.getRawValue().classType === ClassTypes.INTRODUCTORY) {
            this.isPaymentSideNavOpen = true;
          } else {
            this.resetAddScheduleForm();
            this.closeModal.emit();
          }
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAddScheduleParams(): any {
    const addScheduleForm = this.addScheduleForm.getRawValue();

    const scheduleStartDateTime = addScheduleForm.scheduleStartTime
      ? CommonUtils.combineDateAndTime(addScheduleForm.scheduleDate, addScheduleForm.scheduleStartTime)
      : null;

    const scheduleEndDateTime = addScheduleForm.scheduleEndTime
      ? CommonUtils.combineDateAndTime(addScheduleForm.scheduleDate, addScheduleForm.scheduleEndTime)
      : null;

    return {
      ...addScheduleForm,
      scheduleDate: DateUtils.getUtcRangeForLocalDate(addScheduleForm.scheduleDate).startUtc,
      scheduleStartTime: scheduleStartDateTime,
      scheduleEndTime: scheduleEndDateTime,
      isDraftSchedule: true
    };
  }

  initPaymentProcess(cardDetails: CardDetailsResponse | null, scheduleId?: number, studentId?: number) {
    scheduleId = scheduleId ?? this.addScheduleForm.getRawValue().id;
    studentId = studentId ?? this.addScheduleForm.getRawValue().studentId;
    if (!cardDetails) {
      return;
    }
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
      this.toasterService.error(this.constants.errorMessages.addressIncomplete);
      return;
    }
    this.paymentService.showBtnLoader(true);
    if (!cardDetails.isUsingSavedCard) {
      this.paymentService.add(this.getPaymentParams(cardDetails, scheduleId, studentId), API_URL.payment.NMIPayment).subscribe({
        next: res => {
          this.closeViewStudent.emit();
          this.setGoogleAnalyticsPurchaseEvent();
          this.accountManagerSchedulePaymentComponent.setIsPaymentDone(true);
          this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(false);
          this.toasterService.success(this.constants.successMessages.paymentSuccess);
          this.rePaymentParams = null;
          this.refreshScheduleData.emit();
          this.paymentService.showBtnLoader(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.rePaymentParams = { scheduleId, studentId };
          this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(true);
          this.accountManagerSchedulePaymentComponent.setIsPaymentDone(false);
          this.toasterService.error(this.constants.errorMessages.paymentFailed);
          this.showBtnLoader = false;
          this.paymentService.showBtnLoader(false);
          this.cdr.detectChanges();
        }
      });
    } else {
      this.paymentService
        .add(this.getCardDetails(cardDetails, scheduleId, studentId), API_URL.payment.paymentUsingSavedCard)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.closeViewStudent.emit();
            this.setGoogleAnalyticsPurchaseEvent();
            this.accountManagerSchedulePaymentComponent.setIsPaymentDone(true);
            this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(false);
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.refreshScheduleData.emit();
            this.rePaymentParams = null;
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.rePaymentParams = { scheduleId, studentId };
            this.accountManagerSchedulePaymentComponent.setIsRePaymentDone(true);
            this.accountManagerSchedulePaymentComponent.setIsPaymentDone(false);
            this.toasterService.error(this.constants.errorMessages.paymentFailed);
            this.showBtnLoader = false;
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    }
  }

  getCardDetails(cardDetails: CardDetailsResponse, scheduleId?: number, studentId?: number): PaymentParams {
    return {
      dependentInformationId: studentId,
      classType: ClassTypes.INTRODUCTORY,
      scheduleId: scheduleId,
      paidDate: new Date(),
      customerVaultId: cardDetails.customerVaultId,
      transactionType: cardDetails.transactionType,
      paidAmount: cardDetails.paidAmount,
      totalAmount: cardDetails.totalAmount,
      discountedAmount: cardDetails.discountedAmount as number
    };
  }

  getPaymentParams(cardDetails: CardDetailsResponse, scheduleId?: number, studentId?: number): PaymentParams {
    return {
      dependentInformationId: studentId,
      classType: ClassTypes.INTRODUCTORY,
      scheduleId: scheduleId,
      ccNum: cardDetails.number,
      ccExpiry: cardDetails.expiry,
      ccType: cardDetails.type,
      token: cardDetails.token,
      isSaveCard: cardDetails.isSaveCard,
      paidDate: new Date(),
      address: cardDetails.address,
      city: cardDetails.city,
      state: cardDetails.state,
      zip: cardDetails.zip,
      firstName: cardDetails.firstName,
      lastName: cardDetails.lastName,
      transactionType: cardDetails.transactionType,
      paidAmount: cardDetails.paidAmount,
      totalAmount: cardDetails.totalAmount,
      discountedAmount: cardDetails.discountedAmount,
      chequeNumber: cardDetails.chequeNumber
    };
  }

  resetAddScheduleForm(): void {
    this.addScheduleForm.reset();
    this.addScheduleForm.controls.daysOfSchedule.clear();
  }

  onCloseModal(): void {
    this.resetAddScheduleForm();
    this.closeModal.emit();
  }

  asIsOrder() {
    return 1;
  }

  getManagerDetail(student: IdNameModel): void {
    this.authService
      .getUserDetailsFromId(student?.dependentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  getClassName(classType: number): string {
    if (classType === ClassTypes.INTRODUCTORY) {
      return `Introductory ${this.selectedInstrumentName} Lesson`;
    } else {
      if (this.selectedStudentPlan?.isDDDPlan) {
        return `${this.selectedStudentPlan.instrumentName} DDD Plan (${this.selectedStudentPlan?.planDetails?.[0]?.planDetail?.duration})`;
      } else if (this.selectedStudentPlan?.isEnsembleAvailable) {
        return `${this.selectedStudentPlan.instrumentName} Ensemble Plan`;
      }
      return `Weekly Music Lessons - ${this.selectedStudentPlan?.instrumentName} (${this.planSummaryService.getPlanType(
        this.selectedStudentPlan?.planType!
      )} ${this.planSummaryService.getPlanSummary(this.selectedStudentPlan?.planDetails!)})`;
    }
  }

  getLocationNameFromValue(value: number | undefined): string {
    return this.locations.find(name => name?.schoolLocations?.id === value)?.schoolLocations.locationName || '';
  }
}
