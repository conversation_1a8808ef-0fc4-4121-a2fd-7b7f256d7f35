@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.group-class-wrapper {
  background: $header-schedule-bg-color;
  padding: 15px;
  border-radius: 5px;

  .group-class-header {
    @include flex-content-space-between;
    font-size: 16px;
    line-height: 1;
    .name {
      font-weight: 700;
    }

    .class-info-wrapper {
      @include flex-content-align-center;
    }
  }

  .group-class-content-wrapper {
    @include flex-content-align-center;
    font-size: 14px;
    line-height: 1;
    .group-class-content {
      @include flex-content-align-center;

      .img-icon {
        filter: $primary-color-filter;
      }

      .instructor-img,
      .img-icon {
        height: 14px;
        margin-right: 5px;
      }

      .info-content {
        font-weight: 600;

        .info-label {
          font-weight: 700;
        }

        .primary-text {
          color: $primary-color;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .instructor-student-detail {
    padding: 12px 0;
  }
}

.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;

  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }

  .mat-error-position {
    position: relative;
  }
  .instructor-loader-wrapper {
    @include flex-content-align-center;
    margin-bottom: 20px;

    .instructor-loading {
      font-size: 14px;
      font-weight: 600;
    }

    .loader {
      margin-right: 8px;
      border-top: 2px solid $primary-color !important;
    }
  }
}

.field-content {
  @include flex-content-align-center;
  width: 100%;

  .dash {
    margin: 0px 3px;
  }
}

.field-with-mat-inputs {
  margin-bottom: 6px;
}

.btn-typed-option-wrap {
  margin-bottom: 8px;
}

label {
  margin-bottom: 16px;
}

::ng-deep {
  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      font-size: 14px;
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }
  }

  &.time {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }

  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .o-sidebar-wrapper .o-sidebar-body {
    overflow: auto;
    height: calc(100vh - 68px);
    padding: 20px 30px 10px 30px !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-mdc-form-field-error-wrapper {
    padding: 0 !important;
  }

  .special-need {
    .mdc-label {
      font-size: 15px !important;
    }
  }

  .mat-start-date {
    .mat-mdc-text-field-wrapper,
    .mat-mdc-icon-button .mat-mdc-button-touch-target,
    .mat-mdc-button-ripple {
      height: 35px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      height: 35px;
      width: 35px;
    }

    .mat-mdc-icon-button .mat-mdc-button-ripple {
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button svg {
      height: 16px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      padding: 5px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      top: -9px;
    }

    .mat-datepicker-input {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
      border-radius: 9%;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before {
      background: transparent !important;
    }
  }

  .mdc-list-item__primary-text {
    @include w-h-100;
  }
}

@media (max-width: 530px) {
  .o-sidebar-header,
  .field-wrapper {
    flex-wrap: wrap;
  }

  .single-btn-select-wrapper .select-btn {
    padding: 12px 9px;
  }

  .group-class-wrapper {
    .group-class-header {
      flex-wrap: wrap;
      .name {
        margin-bottom: 8px;
      }
    }

    .group-class-content-wrapper {
      flex-wrap: wrap;
      flex-direction: column;
      align-items: flex-start;

      .group-class-content {
        margin-bottom: 8px;
      }
    }

    .dot {
      display: none;
    }
  }
}
