import { Injectable } from '@angular/core';
import { CanLoad, Route, Router, UrlSegment } from '@angular/router';
import { AuthService } from 'src/app/auth/services';
import { ROUTER_PATHS } from '../constants';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard {
  constructor(
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  canMatch(): boolean {
    try {
      const isLoggedIn = this.authService.isLoggedIn;

      if (isLoggedIn) {
        return true;
      }

      const { root, login } = ROUTER_PATHS.auth;
      if (!environment.enableLaunchPage) {
        // Use setTimeout to avoid iOS navigation issues
        setTimeout(() => {
          this.router.navigate([root, login]);
        }, 0);
        return false;
      }

      // Use setTimeout to avoid iOS navigation issues
      setTimeout(() => {
        this.router.navigate([ROUTER_PATHS.launchPage]);
      }, 0);

      return false;
    } catch (error) {
      console.error('AuthGuard error:', error);
      // Fallback navigation for iOS
      setTimeout(() => {
        if (environment.enableLaunchPage) {
          this.router.navigate([ROUTER_PATHS.launchPage]);
        } else {
          this.router.navigate([ROUTER_PATHS.auth.root, ROUTER_PATHS.auth.login]);
        }
      }, 0);
      return false;
    }
  }
}
