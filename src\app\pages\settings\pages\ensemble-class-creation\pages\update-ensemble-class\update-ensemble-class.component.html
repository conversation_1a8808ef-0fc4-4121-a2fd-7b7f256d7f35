<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ isCloneSideNavOpen ? "Clone" : "Edit" }} Ensemble Class</div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="checkCapacity()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : updateEnsembleClassTemplate"></ng-container>
  </div>
</div>

<ng-template #updateEnsembleClassTemplate>
  <div class="group-class-wrapper">
    <div class="group-class-header">
      <div class="name">{{ ensembleClassDetail?.ensembleClassName }} ({{ ensembleClassDetail?.duration }})</div>
      <div class="class-info-wrapper">
        <div>Ensemble</div>
        <div class="dot"></div>
        <div>{{ schedulerService.getLessonType(ensembleClassDetail?.lessonType) }}</div>
      </div>
    </div>
    <div class="group-class-content-wrapper instructor-student-detail">
      <div class="group-class-content">
        <img class="instructor-img" [src]="constants.staticImages.images.profileImgPlaceholder" alt="" />
        <div class="info-content">
          <span class="primary-text">
            @for (assignedInstructors of ensembleClassDetail?.assignedInstructors; track $index) {
            <span *ngIf="$index < 1">
              {{ assignedInstructors.name }}
            </span>
            <div class="dot" *ngIf="$index < 1 && ensembleClassDetail!.assignedInstructors.length - 1 !== $index"></div>
            }
            <div
              class="remaining-instrument-available-count"
              *ngIf="ensembleClassDetail!.assignedInstructors!.length > 1"
              [matTooltip]="getInstrumentNames(ensembleClassDetail!.assignedInstructors)">
              {{ ensembleClassDetail!.assignedInstructors.length - 1 }}+
            </div>
          </span>
        </div>
      </div>
    </div>
    <div class="group-class-content-wrapper">
      <div class="group-class-content">
        <img [src]="constants.staticImages.icons.location" alt="" class="img-icon" />
        <div class="info-content">
          {{ ensembleClassDetail?.locationName }}
        </div>
      </div>
      @if (ensembleClassDetail?.roomName) {
      <div class="dot"></div>
      <div class="group-class-content">
        <img [src]="constants.staticImages.icons.roomIcon" alt="" class="img-icon" />
        <div class="info-content">
          {{ ensembleClassDetail?.roomName }}
        </div>
      </div>
      } @if (ensembleClassDetail?.isWaitlistAvailable) {
      <div class="dot"></div>
      <div class="group-class-content">
        <img [src]="constants.staticImages.icons.checkSquare" alt="" class="img-icon" />
        <div class="info-content">Waitlist Available</div>
      </div>
      }
    </div>
  </div>
  <form [formGroup]="updateEnsembleClassForm" class="mt-3">
    <!-- to be used later -->
    <!-- <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Ensemble Class Name</label>
      <mat-form-field class="mat-select-custom">
        <input matInput placeholder="Group Class Name" formControlName="ensembleClassName" />
        <mat-error>
          <app-error-messages [control]="updateEnsembleClassForm.controls.ensembleClassName"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div> -->

    <div class="field-wrapper field-with-mat-inputs">
      <label>Description</label>
      <mat-form-field>
        <mat-label>Enter description here</mat-label>
        <textarea
          matInput
          formControlName="description"
          cdkTextareaAutosize
          cdkAutosizeMinRows="3"
          cdkAutosizeMaxRows="10"></textarea>
        <mat-error>
          <app-error-messages [control]="updateEnsembleClassForm.controls.description"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>

    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Client Capacity</label>
      <mat-form-field class="mat-select-custom">
        <input matInput type="number" placeholder="Client Capacity" formControlName="studentCapacity" />
        <mat-error>
          <app-error-messages [control]="updateEnsembleClassForm.controls.studentCapacity"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>

    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Start Date</label>
      <div class="field-content">
        <mat-form-field class="mat-start-date w-100">
          <input
            matInput
            [matDatepicker]="startPicker"
            (click)="startPicker.open()"
            (dateInput)="setEnrollDate()"
            (dateChange)="getDayOfWeek()"
            formControlName="scheduleStartDate"
            placeholder="Select Start Date"
            [min]="
              transformedDate! < ensembleClassDetail?.scheduleStartDate!
                ? transformedDate
                : ensembleClassDetail?.scheduleStartDate
            " />
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="updateEnsembleClassForm.controls.scheduleStartDate"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="field-wrapper">
      <label class="required mb-0">Select Weekday(s)</label>
      <div>
        <div class="single-btn-select-wrapper">
          @for (day of constants.daysOfTheWeek; track $index) {
          <div
            class="select-btn"
            [ngClass]="{
              active: updateEnsembleClassForm.controls.daysOfSchedule.value.toString() === day.value.toString()
            }"
            (click)="setFormControlValue('daysOfSchedule', day.value)">
            {{ day.label }}
          </div>
          }
        </div>
        <mat-error
          *ngIf="
            !updateEnsembleClassForm.controls.daysOfSchedule.value &&
            (updateEnsembleClassForm.controls.daysOfSchedule.touched ||
              updateEnsembleClassForm.controls.daysOfSchedule.dirty)
          "
          class="mat-error-position">
          <app-error-messages [control]="updateEnsembleClassForm.controls.daysOfSchedule"></app-error-messages>
        </mat-error>
      </div>
    </div>

    @if (getInstructorAvailability.scheduleStartDate && getInstructorAvailability.scheduleEndDate &&
    getInstructorAvailability.locationId && getInstructorAvailability.daysOfSchedule &&
    getInstructorAvailability.duration) {
    <div class="field-wrapper">
      <label class="required mb-0">Select Instructor</label>
      <div class="w-100 mat-select-custom time">
        @if (showInstructorLoader) {
        <ng-container [ngTemplateOutlet]="showSpinner"></ng-container>
        } @else {
        <app-multi-select-chips
          [filterDetail]="filterParams.instructor"
          (selectedFilterValues)="getSuggestedTime(); setInstructorIdDetails()"></app-multi-select-chips>
        <mat-error
          *ngIf="
            !updateEnsembleClassForm.controls.assignedInstructors.value &&
            (updateEnsembleClassForm.controls.assignedInstructors.touched ||
              updateEnsembleClassForm.controls.assignedInstructors.dirty)
          "
          class="mat-error-position">
          <app-error-messages [control]="updateEnsembleClassForm.controls.assignedInstructors"></app-error-messages>
        </mat-error>
        }
      </div>
    </div>
    }

    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Select Time Slot</label>
      <div class="w-100">
        <mat-form-field class="w-100 mat-select-custom time">
          <mat-select [placeholder]="selectedTimeSlot ?? 'Select Time'" (opened)="getSuggestedTime()">
            <mat-option
              *ngFor="let suggestedTimeSlot of suggestedTimeSlots"
              (click)="setStartAndEndTime(suggestedTimeSlot)"
              [value]="suggestedTimeSlot.startTime + ' - ' + suggestedTimeSlot.endTime">
              {{ suggestedTimeSlot.startTime | date : "shortTime" }} -
              {{ suggestedTimeSlot.endTime | date : "shortTime" }}
            </mat-option>
            @if (!suggestedTimeSlots?.length) {
            <mat-option>No Time Slot Available</mat-option>
            }
          </mat-select>
        </mat-form-field>
        <mat-error>
          <app-error-messages [control]="updateEnsembleClassForm.controls.scheduleStartTime"></app-error-messages>
        </mat-error>
      </div>
    </div>

    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Select Revenue Category</label>
      <div class="w-100">
        <mat-form-field class="w-100 mat-select-custom">
          <mat-select formControlName="categoryId" placeholder="Select Revenue Category">
            <mat-option *ngFor="let revenueCategory of revenueCategories" [value]="revenueCategory.category.id">
              {{ revenueCategory.category.categoryName }}
            </mat-option>
          </mat-select>
          <mat-error>
            <app-error-messages [control]="updateEnsembleClassForm.controls.categoryId"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
<ng-template #showSpinner>
  <div class="instructor-loader-wrapper">
    <div class="loader"></div>
    <span class="instructor-loading">Please wait while the instructors are loading...</span>
  </div>
</ng-template>
