<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav [opened]="isPlanRenewSideNavOpen || isPDFViewerSideNavOpen" mode="over" position="end" fixedInViewport="true"
    class="sidebar-w-750" [disableClose]="true">
    @if(isPlanRenewSideNavOpen) {
      <app-continue-to-checkout
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        [selectedInstrumentName]="selectedInstrumentName"
        [bookPlanFormValue]="bookPlanFormValue"
        [isPlanRenewal]="true"
        (closeSideNav)="isPlanRenewSideNavOpen = false"
        (closeAllSideNav)="isPlanRenewSideNavOpen = false; getStudentPlansExpiringSoon((currentPage = 1), pageSize)"
      ></app-continue-to-checkout>
    }
    @if(isPDFViewerSideNavOpen) {
      <app-pdf-viewer
        [documentInfo]="documentInfo"
        [isPlanRenewal]="true"
        (closeSideNav)="isPDFViewerSideNavOpen = false; getStudentPlansExpiringSoon((currentPage = 1), pageSize)"></app-pdf-viewer>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="auth-page-with-header">
  <div class="filter-and-count-wrapper align-baseline mb-2">
    <div class="search-and-count-wrapper-auth">
      <div class="search-and-count-wrapper">
        <div class="total-users">
          Total: <span class="border-0">{{ totalCount }}</span>
        </div>
      </div>
    </div>
    <div class="filter-wrapper">
      <mat-form-field class="search-bar-wrapper">
        <mat-select
          [(ngModel)]="renewPlanFilter"
          (selectionChange)="getStudentPlansExpiringSoon((currentPage = 1), pageSize)">
          <mat-option *ngFor="let planRenewal of planRenewalStatus | enumToKeyValue" [value]="planRenewal.value">
            {{ planRenewal.key.replace('_',' ') | titlecase }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : studentPlanLists"></ng-container>
</div>

<ng-template #studentPlanLists>
  <ng-container [ngTemplateOutlet]="studentPlans && studentPlans.length ? studentPlanList : noDataFound"></ng-container>
</ng-template>

<ng-template #studentPlanList>
  <div class="visits-list">
    @for (studentPlan of studentPlans | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "plan-renewal" }; track $index) {
    <div class="o-card mb-2">
      <div class="o-card-body">
        <div class="pointer" (click)="openStudentDetailPage(studentPlan.studentplan.dependentInformationId)">
          <div class="title">
            @switch (true) {
              @case (studentPlan.isDDDPlan) {
                {{ studentPlan.instrumentName }} DDD Plan ({{ studentPlan?.planDetails?.[0]?.planDetail?.duration }})
              }
              @case (studentPlan.isRentalPlan) {
                {{ studentPlan.instrumentName }} Rental Plan {{ studentPlan.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
              }
              @case (studentPlan.isEnsembleAvailable) {
                Weekly Ensemble Lessons ({{ planSummaryService.getPlanType(studentPlan.planType) }}-{{
                planSummaryService.getPlanSummary(studentPlan.planDetails)
              }})
              }
              @default {
                Weekly {{ studentPlan.instrumentName }} Lessons ({{ planSummaryService.getPlanType(studentPlan.planType) }}-{{
                  planSummaryService.getPlanSummary(studentPlan.planDetails)
                }})                
              }
            }
          </div>

          <div class="visit-content">
            <div class="visit-info">
              <div class="me-1 text-black">Dependent</div>
              <div class="primary-color">
                {{ studentPlan.dependentName }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="text-gray">
                {{ studentPlan.startDate | date: 'mediumDate' }} - {{ studentPlan.endDate | date: 'mediumDate' }}
              </div>
            </div>
            @if(currentDate < studentPlan.endDate && !studentPlan.isAgreementDone) {
              <div class="dot"></div>
              <div class="visit-info">
                  Expires in 
                <div class="primary-color ms-1">
                  {{ getRemainingDays(studentPlan.endDate) }} Days
                </div>
              </div>
            }
          </div>
        </div>

        <div class="visit-cancel-info">
          @if (!studentPlan.updatedAssignedPlanStatus) {
            <button
              [appLoader]="showBtnLoaderId === studentPlan.studentplan.id"
              mat-raised-button
              color="primary"
              class="mat-primary-btn"
              type="button"
              (click)="togglePlanRenewalSideNav(true, studentPlan)"
            >
              Renew Plan
            </button>
          }
          @else {
            <button
              [appLoader]="showBtnLoaderId === studentPlan.studentplan.id"
              mat-raised-button
              color="primary"
              class="mat-primary-btn"
              type="button"
              (click)="togglePDFViewerSideNav(true, studentPlan)"
            >
              {{ studentPlan.isAgreementDone ? 'View PDF' : 'Accept T&C' }}
            </button>
          }
        </div>
      </div>
    </div>
    }
  </div>
  @if (totalCount > 10) {
    <pagination-controls
      id="plan-renewal"
      [previousLabel]="''"
      [nextLabel]="''"
      (pageChange)="onPageChange($event)"
      [responsive]="true"
      class="pagination-controls"></pagination-controls>
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>