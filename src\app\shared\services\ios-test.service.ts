import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../auth/services/auth.service';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class IOSTestService {
  private isIOS: boolean;
  private testResults: any[] = [];

  constructor(
    private router: Router,
    private authService: AuthService,
    private localStorageService: LocalStorageService
  ) {
    this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  async runIOSTests(): Promise<any[]> {
    if (!this.isIOS) {
      console.log('Not running on iOS, skipping iOS-specific tests');
      return [];
    }

    console.log('Starting iOS compatibility tests...');
    this.testResults = [];

    // Test 1: localStorage functionality
    await this.testLocalStorage();

    // Test 2: Navigation functionality
    await this.testNavigation();

    // Test 3: Authentication service
    await this.testAuthService();

    // Test 4: Viewport and orientation
    await this.testViewport();

    // Test 5: Memory and performance
    await this.testPerformance();

    console.log('iOS tests completed:', this.testResults);
    return this.testResults;
  }

  private async testLocalStorage(): Promise<void> {
    try {
      const testKey = 'ios-test-key';
      const testValue = 'ios-test-value';
      
      // Test set
      this.localStorageService.setItem(testKey, testValue);
      
      // Test get
      const retrievedValue = this.localStorageService.getItem(testKey);
      
      // Test remove
      this.localStorageService.removeItem(testKey);
      
      const success = retrievedValue === testValue;
      this.testResults.push({
        test: 'localStorage',
        success: success,
        message: success ? 'localStorage working correctly' : 'localStorage failed',
        details: { testValue, retrievedValue }
      });
    } catch (error) {
      this.testResults.push({
        test: 'localStorage',
        success: false,
        message: 'localStorage error',
        error: error
      });
    }
  }

  private async testNavigation(): Promise<void> {
    try {
      const currentUrl = this.router.url;
      const canNavigate = this.router.navigateByUrl !== undefined;
      
      this.testResults.push({
        test: 'navigation',
        success: canNavigate,
        message: canNavigate ? 'Router navigation available' : 'Router navigation unavailable',
        details: { currentUrl, canNavigate }
      });
    } catch (error) {
      this.testResults.push({
        test: 'navigation',
        success: false,
        message: 'Navigation test error',
        error: error
      });
    }
  }

  private async testAuthService(): Promise<void> {
    try {
      const isLoggedIn = this.authService.isLoggedIn;
      const hasIsLoggedInSubject = this.authService.isLoggedIn$ !== undefined;
      
      this.testResults.push({
        test: 'authService',
        success: hasIsLoggedInSubject,
        message: hasIsLoggedInSubject ? 'AuthService working correctly' : 'AuthService issues detected',
        details: { isLoggedIn, hasIsLoggedInSubject }
      });
    } catch (error) {
      this.testResults.push({
        test: 'authService',
        success: false,
        message: 'AuthService test error',
        error: error
      });
    }
  }

  private async testViewport(): Promise<void> {
    try {
      const viewport = {
        innerWidth: window.innerWidth,
        innerHeight: window.innerHeight,
        outerWidth: window.outerWidth,
        outerHeight: window.outerHeight,
        devicePixelRatio: window.devicePixelRatio,
        orientation: (screen as any).orientation?.type || 'unknown'
      };

      const hasValidViewport = viewport.innerWidth > 0 && viewport.innerHeight > 0;
      
      this.testResults.push({
        test: 'viewport',
        success: hasValidViewport,
        message: hasValidViewport ? 'Viewport information available' : 'Viewport issues detected',
        details: viewport
      });
    } catch (error) {
      this.testResults.push({
        test: 'viewport',
        success: false,
        message: 'Viewport test error',
        error: error
      });
    }
  }

  private async testPerformance(): Promise<void> {
    try {
      const performanceAvailable = performance !== undefined;
      const memoryInfo = (performance as any).memory || null;
      const navigationTiming = performance.getEntriesByType('navigation')[0] || null;
      
      this.testResults.push({
        test: 'performance',
        success: performanceAvailable,
        message: performanceAvailable ? 'Performance API available' : 'Performance API unavailable',
        details: {
          performanceAvailable,
          hasMemoryInfo: !!memoryInfo,
          hasNavigationTiming: !!navigationTiming,
          memoryUsed: memoryInfo ? Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) + 'MB' : 'N/A'
        }
      });
    } catch (error) {
      this.testResults.push({
        test: 'performance',
        success: false,
        message: 'Performance test error',
        error: error
      });
    }
  }

  getTestResults(): any[] {
    return this.testResults;
  }

  displayTestResults(): void {
    if (!this.isIOS) return;

    const resultsDiv = document.createElement('div');
    resultsDiv.id = 'ios-test-results';
    resultsDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border: 2px solid #333;
      padding: 20px;
      border-radius: 10px;
      z-index: 10001;
      max-width: 90%;
      max-height: 80%;
      overflow-y: auto;
      font-family: Arial, sans-serif;
      font-size: 14px;
    `;

    let html = '<h3>iOS Test Results</h3>';
    this.testResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      html += `
        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
          <strong>${status} ${result.test}</strong><br>
          ${result.message}<br>
          ${result.details ? '<small>' + JSON.stringify(result.details, null, 2) + '</small>' : ''}
          ${result.error ? '<small style="color: red;">' + result.error.message + '</small>' : ''}
        </div>
      `;
    });

    html += '<button onclick="document.getElementById(\'ios-test-results\').remove()">Close</button>';
    resultsDiv.innerHTML = html;
    document.body.appendChild(resultsDiv);
  }
}
