import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import * as Sentry from '@sentry/angular-ivy';
import { AppModule } from './app/app.module';
import { Constants } from './app/shared/constants';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

if (false) {
  Sentry.init({
    dsn: environment.sentryKey,
    integrations: [
      // Registers and configures the Tracing integration,
      // which automatically instruments application to monitor its
      // performance, including custom Angular routing instrumentation
      new Sentry.BrowserTracing({
        routingInstrumentation: Sentry.routingInstrumentation
      }),
      new Sentry.Replay()
    ],
    // Set `tracePropagationTargets` to control for which URLs distributed tracing should be enabled
    tracePropagationTargets: Constants.environmentsForErrorTracing,
    // Set tracesSampleRate to 1.0 to capture 100%
    // of transactions for performance monitoring.
    // values range from 0 to 1, 0.2 signifies 20% of transactions will be captured.
    tracesSampleRate: 1.0
    // replaysSessionSampleRate: 0.1,
    // replaysOnErrorSampleRate: 1.0,
  });
}

// iOS-specific error handling
const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

if (isIOS) {
  console.log('iOS device detected, applying iOS-specific configurations');

  // Handle iOS-specific errors
  window.addEventListener('error', (event) => {
    console.error('iOS Error:', event.error);
    console.error('Error details:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    });
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('iOS Unhandled Promise Rejection:', event.reason);
    console.error('Promise rejection details:', {
      reason: event.reason,
      promise: event.promise
    });
  });
}

platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .then(() => {
    if (isIOS) {
      console.log('Angular app successfully bootstrapped on iOS');
    }
  })
  .catch((err) => {
    console.error('Bootstrap error:', err);
    if (isIOS) {
      console.error('iOS Bootstrap error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });

      // Try to show a user-friendly error message
      document.body.innerHTML = `
        <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
          <h2>Loading Error</h2>
          <p>There was an issue loading the application on your device.</p>
          <p>Please try refreshing the page or clearing your browser cache.</p>
          <button onclick="window.location.reload()" style="padding: 10px 20px; font-size: 16px;">
            Refresh Page
          </button>
        </div>
      `;
    }
  });

const script = document.createElement('script');
script.type = 'text/javascript';
script.src = 'https://secure.networkmerchants.com/token/Collect.js?sandbox=true';
script.setAttribute('data-tokenization-key', environment.paymentTokenizationKey);
script.setAttribute('data-variant', 'inline');
document.head.appendChild(script);  
