<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isViewSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true">
    @if (isViewSideNavOpen) {
      <app-cancel-request-sidenav
        [selectedRequestId]="selectedRequestId"
        (closeSideNav)="toggleSideNav(false, undefined)"
        (refreshData)="getCancelPlanRequest(currentPage, pageSize)"></app-cancel-request-sidenav>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="auth-page-with-header">
      <div class="search-and-count-wrapper-auth">
        <div class="search-and-count-wrapper">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
        </div>
        <div class="filter-wrapper">
          <mat-form-field class="search-bar-wrapper">
            <mat-select
              [(ngModel)]="filters.planTypeFilter"
              (selectionChange)="getCancelPlanRequest((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Plan Types</mat-option>
              <mat-option *ngFor="let planType of planTypes | enumToKeyValue" [value]="planType.value">
                {{ planType.key | titlecase }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field class="search-bar-wrapper ms-2">
            <mat-select
              [(ngModel)]="filters.DurationFilter"
              (selectionChange)="getCancelPlanRequest((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Durations</mat-option>
              <mat-option *ngFor="let duration of durations | enumToKeyValue" [value]="duration.value">
                {{ duration.value }} Minutes
              </mat-option>
            </mat-select>
          </mat-form-field>
          <!-- to be used -->
          <!-- <mat-form-field class="search-bar-wrapper ms-2">
            <mat-select
              [(ngModel)]="filters.VisitsPerWeekFilter"
              (selectionChange)="getCancelPlanRequest((currentPage = 1), pageSize)">
              <mat-option [value]="all.ALL">All Visits per week</mat-option>
              <mat-option *ngFor="let visit of visits | enumToKeyValue" [value]="visit.value">
                {{ visit.value }} Visit
              </mat-option>
            </mat-select>
          </mat-form-field> -->
        </div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell first-cell">Plan Name</div>
              <div class="o-cell">Requested On</div>
              <div class="o-cell">Reviewed On</div>
              <div class="o-cell">Reason</div>
              <div class="o-cell">Action</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content" [ngClass]="totalCount > 10 ? 'show-pagination' : 'hide-pagination'">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : studentTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
      @if (totalCount > 10) {
        <pagination-controls
          id="plan-cancel"
          [previousLabel]="''"
          [nextLabel]="''"
          (pageChange)="onPageChange($event)"
          [responsive]="true"
          class="pagination-controls"></pagination-controls>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #studentTemplate>
  <ng-container [ngTemplateOutlet]="totalCount ? studentTableContent : noDataFound"></ng-container>
</ng-template>

<ng-template #studentTableContent>
  @for (
    cancelPlanRequest of cancelPlanRequests
      | paginate: { itemsPerPage: pageSize, currentPage: currentPage, totalItems: totalCount, id: "plan-cancel" };
    track $index
  ) {
    <div class="o-row">
      <div class="o-cell first-cell instructor-name-photo-wrapper">
        <div class="student-name-wrapper">
          <div class="student-name">
            @switch (true) {
              @case (cancelPlanRequest.isDDDPlan) {
                {{ cancelPlanRequest.instrumentName }} DDD Plan ({{ cancelPlanRequest?.planDetails?.[0]?.planDetail?.duration }})
              }
              @case (cancelPlanRequest.isRentalPlan) {
                {{ cancelPlanRequest.instrumentName }} Rental Plan {{ cancelPlanRequest.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
              }
              @default {
                Weekly {{ cancelPlanRequest.studentDiscontinuedPlanDetails?.isEnsembleAvailable ? "Ensemble" : cancelPlanRequest.instrumentName }} Lessons ({{ planSummaryService.getPlanType(cancelPlanRequest.planType) }}-{{ planSummaryService.getPlanSummary(cancelPlanRequest.planDetails) }})
              }
            } 
          </div>
        </div>
      </div>
      <div class="o-cell text-gray">
        {{ cancelPlanRequest.studentDiscontinuedPlanDetails?.requestDate | localDate | date | dashIfEmpty }}
        <div>
          From <span class="text-black pointer" (click)="openStudentDetails(cancelPlanRequest.studentplan.dependentInformationId)">{{ cancelPlanRequest.dependentName | dashIfEmpty }}</span>
        </div>
      </div>
      <div class="o-cell text-gray">
        @if (cancelPlanRequest.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planCancelStatus.APPROVED) {
          {{ cancelPlanRequest.studentDiscontinuedPlanDetails?.approvedDate | localDate | date | dashIfEmpty }}
          <div>
            By
            <span class="text-black">{{
              cancelPlanRequest.studentDiscontinuedPlanDetails?.approvedBy | dashIfEmpty
            }}</span>
          </div>
        } @else if (
          cancelPlanRequest.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planCancelStatus.CANCELED
        ) {
          {{ cancelPlanRequest.studentDiscontinuedPlanDetails?.canceledDate | localDate | date | dashIfEmpty }}
          <div>
            By
            <span class="text-black">{{
              cancelPlanRequest.studentDiscontinuedPlanDetails?.canceledBy | dashIfEmpty
            }}</span>
          </div>
        } @else {
          -
        }
      </div>
      <div class="o-cell text-gray">
        {{ cancelPlanRequest.studentDiscontinuedPlanDetails?.reason | titlecase | dashIfEmpty }}
      </div>
      <div class="o-cell text-gray d-flex justify-content-center">
        @switch (cancelPlanRequest.studentDiscontinuedPlanDetails?.planCancelRequestStatus) {
          @case (planCancelStatus.PENDING) {
            <div class="icons">
              <img
                matTooltip="Approve" alt=""
                [src]="constants.staticImages.icons.checkCircle"
                class="approve me-3"
                (click)="
                  approveRequestConfirmation(cancelPlanRequest.studentDiscontinuedPlanDetails?.planCancelRequestId)
                " />
              <img
                matTooltip="Cancel"
                [src]="constants.staticImages.icons.crossCircle"
                class="cancel" alt=""
                (click)="toggleSideNav(true, cancelPlanRequest.studentDiscontinuedPlanDetails?.planCancelRequestId)" />
            </div>
          }
          @case (planCancelStatus.APPROVED) {
            <div class="status enrolled">Approved</div>
          }
          @case (planCancelStatus.CANCELED) {
            <div class="status canceled">Canceled</div>
          }
        }
      </div>
    </div>

    @if ($index < cancelPlanRequests.length - 1) {
      <div class="dotted-divider"></div>
    }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
