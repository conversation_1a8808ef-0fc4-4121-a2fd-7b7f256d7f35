<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  @if (isEditMaintenanceRequest || isViewMaintenanceRequest || isUpdateMaintenanceRequest) {
  <mat-sidenav
    [opened]="isEditMaintenanceRequest || isViewMaintenanceRequest || isUpdateMaintenanceRequest"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="'sidebar-w-750'"
    [disableClose]="true"
  >
    @if (isViewMaintenanceRequest) {
    <app-view-maintenance-request
      (closeModal)="isViewMaintenanceRequest = false"
      [selectedMaintenanceRequestDetails]="selectedMaintenanceRequestDetails"
      (updateMaintenanceRequestList)="updateMaintenanceRequestList.emit()"
      [closeMaintenanceRequestConfirmation]="closeMaintenanceRequestConfirmation.bind(this)"
    ></app-view-maintenance-request>
    } @else if(isEditMaintenanceRequest) {
    <app-add-edit-maintenance-request
      (closeModal)="isEditMaintenanceRequest = false"
      (updateMaintenanceRequestList)="updateMaintenanceRequestList.emit()"
      [selectedMaintenanceRequestDetails]="selectedMaintenanceRequestDetails"
    ></app-add-edit-maintenance-request>
    } @else {
    <app-update-maintenance-request-status
      (closeModal)="isUpdateMaintenanceRequest = false"
      (updateMaintenanceRequestList)="updateMaintenanceRequestList.emit()"
      [selectedMaintenanceRequestDetails]="selectedMaintenanceRequestDetails"
    ></app-update-maintenance-request-status>
    }
  </mat-sidenav>
  }

  <mat-sidenav-content>
    <div class="auth-page-wrapper auth-page-with-header">
      <div class="filter-and-count-wrapper">
        <div class="search-and-count-wrapper-auth">
          <div class="total-users">
            Total: <span>{{ maintenanceRequests?.openMaintenanceRequestCount }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input matInput placeholder="Search.." [(ngModel)]="searchTerm" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
        <div class="filter-wrapper"></div>
      </div>

      <div class="o-card">
        <div class="o-card-body">
          <div class="o-table">
            <div class="o-row o-header">
              <div class="o-cell text-start first-cell">Request Summary</div>
              <div class="o-cell">Created on</div>
              <div class="o-cell">Instrument</div>
              <div class="o-cell">Location</div>
              <div class="o-cell">Room No.</div>
              <div class="o-cell">Status</div>
              <div class="o-cell">Action</div>
            </div>
            <div class="dotted-divider"></div>
            <div class="content">
              <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : openRequestTemplate"></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #openRequestTemplate>
  <ng-container
    [ngTemplateOutlet]="maintenanceRequests?.openMaintenanceRequestCount ? openMaintenanceRequestTableContent : noDataFound"
  ></ng-container>
</ng-template>

<ng-template #openMaintenanceRequestTableContent>
  @for ( maintenanceRequest of maintenanceRequests!.openMaintenanceRequest | filterByTitle: searchTerm; track $index ) {
  <div class="o-row">
    <div class="o-cell text-start pointer" (click)="openViewMaintenanceModal(maintenanceRequest)">{{ maintenanceRequest.title }}</div>
    <div class="o-cell text-gray pointer" (click)="openViewMaintenanceModal(maintenanceRequest)">
      <div>{{ maintenanceRequest.createdOn | localDate | date : constants.dateFormats.MMM_D_Y }}</div>
      <div>From <span class="text-black">{{ maintenanceRequest.createdByName }}</span></div>
    </div>
    <div class="o-cell pointer" (click)="openViewMaintenanceModal(maintenanceRequest)">
      {{ maintenanceRequest.instrumentName | dashIfEmpty }}
    </div>
    <div class="o-cell text-gray">
      <div class="d-flex justify-content-center align-items-center">
        <img [src]="constants.staticImages.icons.location" alt="" />
        {{ maintenanceRequest.locationName }}
      </div>
    </div>
    <div class="o-cell text-gray">{{ maintenanceRequest.roomName | dashIfEmpty }}</div>
    <div class="o-cell text-gray">
      <ng-container *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <img
          [src]="constants.staticImages.icons.editPenGray"
          alt="pen"
          class="status-update"
          (click)="openUpdateMaintenanceRequestModal(maintenanceRequest)"
          height="20"
        />
      </ng-container>
      @switch (maintenanceRequest.status) { @case (maintenanceRequestsStatus.InProgress) {
      <span class="in-progress-request">{{ maintenanceRequestsStatusLabel.InProgress }}</span>
      } @case (maintenanceRequestsStatus.OnHold) {
      <span class="on-hold-request">{{ maintenanceRequestsStatusLabel.OnHold }}</span>
      } @default {
      <span class="open-request">{{ maintenanceRequestsStatusLabel.Open }}</span>
      } }
    </div>
    <div class="o-cell text-gray">
      <ng-container *appHasPermission="[constants.roles.INSTRUCTOR, constants.roles.SUPERVISOR]">
        <img
          [src]="constants.staticImages.icons.editPenGray"
          alt="pen"
          class="editImg"
          (click)="openEditMaintenanceModal(maintenanceRequest)"
        />
        <img
          [src]="constants.staticImages.icons.trash"
          alt="trash"
          (click)="deleteMaintenanceRequestConfirmation(maintenanceRequest.id)"
          class="editImg"
        />
      </ng-container>
      <ng-container *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <img
          [src]="constants.staticImages.icons.crossCircle"
          alt=""
          class="editImg"
          matTooltip="Close Request"
          (click)="closeMaintenanceRequestConfirmation(maintenanceRequest.id)"
        />
      </ng-container>
    </div>
  </div>

  @if ($index < maintenanceRequests?.openMaintenanceRequest!.length - 1) {
  <div class="dotted-divider"></div>
  } }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
