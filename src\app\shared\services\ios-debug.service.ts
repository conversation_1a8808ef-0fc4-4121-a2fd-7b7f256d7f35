import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class IOSDebugService {
  private isIOS: boolean;
  private debugEnabled: boolean = true;

  constructor() {
    this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (this.isIOS) {
      this.initializeIOSDebugging();
    }
  }

  private initializeIOSDebugging(): void {
    console.log('iOS Debug Service initialized');
    
    // Create a debug panel for iOS
    this.createDebugPanel();
    
    // Monitor performance
    this.monitorPerformance();
    
    // Monitor memory usage
    this.monitorMemory();
  }

  private createDebugPanel(): void {
    if (!this.debugEnabled || !this.isIOS) return;

    const debugPanel = document.createElement('div');
    debugPanel.id = 'ios-debug-panel';
    debugPanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 10000;
      max-width: 200px;
      display: none;
    `;
    
    // Toggle debug panel with triple tap
    let tapCount = 0;
    document.addEventListener('touchend', () => {
      tapCount++;
      setTimeout(() => {
        if (tapCount === 3) {
          debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
        }
        tapCount = 0;
      }, 300);
    });

    document.body.appendChild(debugPanel);
    this.updateDebugPanel();
  }

  private updateDebugPanel(): void {
    const panel = document.getElementById('ios-debug-panel');
    if (!panel) return;

    const info = {
      userAgent: navigator.userAgent.substring(0, 50) + '...',
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      orientation: (screen as any).orientation?.type || 'unknown',
      online: navigator.onLine,
      memory: (performance as any).memory ? 
        `${Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB` : 'N/A'
    };

    panel.innerHTML = `
      <strong>iOS Debug Info</strong><br>
      UA: ${info.userAgent}<br>
      Viewport: ${info.viewport}<br>
      Orientation: ${info.orientation}<br>
      Online: ${info.online}<br>
      Memory: ${info.memory}<br>
      <small>Triple tap to toggle</small>
    `;
  }

  private monitorPerformance(): void {
    if (!this.isIOS) return;

    setInterval(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        console.log('iOS Performance:', {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 'N/A',
          firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 'N/A'
        });
      }
      this.updateDebugPanel();
    }, 5000);
  }

  private monitorMemory(): void {
    if (!this.isIOS || !(performance as any).memory) return;

    setInterval(() => {
      const memory = (performance as any).memory;
      console.log('iOS Memory Usage:', {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      });
    }, 10000);
  }

  logError(error: any, context: string): void {
    if (!this.isIOS) return;

    console.error(`iOS Error in ${context}:`, {
      message: error.message,
      stack: error.stack,
      name: error.name,
      context: context,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    });
  }

  logNavigation(from: string, to: string, success: boolean): void {
    if (!this.isIOS) return;

    console.log('iOS Navigation:', {
      from: from,
      to: to,
      success: success,
      timestamp: new Date().toISOString()
    });
  }

  checkLocalStorage(): boolean {
    try {
      localStorage.setItem('ios-test', 'test');
      localStorage.removeItem('ios-test');
      return true;
    } catch (error) {
      this.logError(error, 'localStorage check');
      return false;
    }
  }

  getIOSInfo(): any {
    if (!this.isIOS) return null;

    return {
      isIOS: this.isIOS,
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      vendor: navigator.vendor,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio
      },
      localStorage: this.checkLocalStorage()
    };
  }
}
