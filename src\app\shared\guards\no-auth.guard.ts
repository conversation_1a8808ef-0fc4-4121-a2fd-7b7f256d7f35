import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ROUTER_PATHS } from '../constants';
import { AuthService } from 'src/app/auth/services';

@Injectable({
  providedIn: 'root'
})
export class NoAuthGuard {
  constructor(
    private readonly router: Router,
    private readonly authService: AuthService
  ) {}

  canMatch(): boolean {
    try {
      const isLoggedIn = this.authService.isLoggedIn;
      const navigation = this.router.getCurrentNavigation();
      const isResetPasswordPage = navigation?.initialUrl?.root?.children?.['primary']?.segments?.some(
        (segment) => segment.path.includes('forgot-password')
      ) || false;

      if (isLoggedIn && isResetPasswordPage) {
        return true;
      }

      if (isLoggedIn) {
        // Use setTimeout to avoid iOS navigation issues
        setTimeout(() => {
          this.router.navigate([ROUTER_PATHS.dashboard.root]);
        }, 0);
        return false;
      }

      return true;
    } catch (error) {
      console.error('NoAuthGuard error:', error);
      // Allow access on error to prevent blocking
      return true;
    }
  }
}
