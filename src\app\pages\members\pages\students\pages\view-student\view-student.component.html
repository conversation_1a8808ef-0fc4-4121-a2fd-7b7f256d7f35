<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isViewDocumentSideNavOpen || isScheduleALesson || isAddDependentSideNavOpen || isEditAccountManagerSideNavOpen || isMessageSideNavOpen || isPaymentMethodSideNavOpen || isPlanRenewalSideNavOpen || isPastVisitSideNavOpen || isEditLessonSideNavOpen || isInstructorDetailsSideNavOpen || isIntroductoryPaymentSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true"
  >
    @if (isViewDocumentSideNavOpen) {
      <app-view-student-documents
        [selectedStudentDetails]="selectedStudentDetails"
        [studentDocuments]="studentDocuments"
        (closeSideNav)="isViewDocumentSideNavOpen = false"
      ></app-view-student-documents>
    } @if (isScheduleALesson) {
      <app-add-schedule
        (closeModal)="isScheduleALesson = false"
        [accManagerDetails]="accManagerDetails"
        (closeViewStudent)="onLessonSchedule(); refreshStudentList.emit()"
        (refreshScheduleData)="getDependentSchedule(); getStudentGrades(selectedStudentDetails?.id!)"
        [isIntroductoryLesson]="true"
        [selectedStudentId]="selectedStudentId"
      ></app-add-schedule>
    } @if (isAddDependentSideNavOpen) {
      <app-add-student
        [selectedAccManagerDetail]="accManagerDetails"
        [isAddDependent]="true"
        (closeSideNav)="isAddDependentSideNavOpen = false"
        (refreshStudentList)="getAccountManagerDetails(); refreshStudentList.emit()"
      ></app-add-student>
    }
    @if (isEditAccountManagerSideNavOpen) {
      <app-add-student
        [selectedAccManagerDetail]="accManagerDetails"
        [isAddDependent]="false"
        [isEditAccountManager]="true"
        (closeSideNav)="isEditAccountManagerSideNavOpen = false"
        (refreshStudentList)="getAccountManagerDetails(); refreshStudentList.emit()"
      ></app-add-student>
    }
    @if (isMessageSideNavOpen) {
      <app-messages
        [selectedIdEmail]="selectedIdEmail"
        (closeSideNav)="toggleMessageSideNav(false)"
      ></app-messages>
    }
    @if (isPaymentMethodSideNavOpen) {
      <div class="o-sidebar-wrapper">
        <div class="o-sidebar-header">
            <div class="title">Payment Methods</div>
            <div class="action-btn-wrapper">
              <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="isPaymentMethodSideNavOpen = false"> Close </button>
            </div>
        </div>
        <div class="divider"></div>
        <div class="o-sidebar-body">
            <app-payment-methods [screen]="'billing-screen'" [accManagerDetails]="accManagerDetails"></app-payment-methods>
        </div>
      </div>
    }
    @if (isPlanRenewalSideNavOpen) {
      <app-continue-to-checkout
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        [selectedInstrumentName]="selectedInstrumentName"
        [bookPlanFormValue]="bookPlanFormValue"
        [isPlanRenewal]="true"
        (closeSideNav)="isPlanRenewalSideNavOpen = false"
        (closeAllSideNav)="isPlanRenewalSideNavOpen = false; getExpiringPlans()"
      ></app-continue-to-checkout>
    }
    @if (isPastVisitSideNavOpen) {
      <app-student-past-visit
        [selectedStudentDetails]="selectedStudentDetails"
        [studentAttendances]="studentAttendance"
        [totalCount]="attendanceTotalCount"
        [selectedTabOption]="selectedTabOption"
        (closeSideNav)="isPastVisitSideNavOpen = false"
        (getStudentAttendance)="getStudentAttendance($event.page, $event.pageSize, $event.status)"
      ></app-student-past-visit>
    }
    @if (isEditLessonSideNavOpen) {
      <app-update-schedule
        [selectedEvent]="selectedEvent!"
        (closeModal)="isEditLessonSideNavOpen = false"
        (refreshScheduleData)="getDependentSchedule()"
      ></app-update-schedule>
    }
    @if (isInstructorDetailsSideNavOpen) {
      <app-view-instructor
        [selectedInstructorViewDetails]="selectedInstructorDetails"
        [isFromScheduler]="true"
        (closeViewSideNav)="isInstructorDetailsSideNavOpen = false"
      ></app-view-instructor>
    }
    @if (isIntroductoryPaymentSideNavOpen) {
      <app-account-manager-schedule-payment
        [accManagerDetails]="accManagerDetails"
        [scheduleInfo]="selectedEvent"
        (closeEnrollmentSideNav)="onInitiatePaymentForIntroductoryClass(false, null)"
        (scheduleIntroductoryLesson)="initPaymentProcess($event)"
        (rePaymentForTheSchedule)="initPaymentProcess($event, rePaymentParams?.scheduleId, rePaymentParams?.studentId)">
      </app-account-manager-schedule-payment>
    }
  </mat-sidenav>
</mat-sidenav-container>

<mbsc-popup
  class="md-tooltip"
  #eventDetailsPopup
  [anchor]="detailsAnchor"
  [options]="popupOptions"
  (onClose)="schedulerDetailPopupComponent.showCancelLessonView = false"
>
  <app-scheduler-detail-popup
    [selectedEvent]="selectedEvent"
    [isFromViewStudent]="true"
    (editLesson)="onEditLesson($event)"
    (closePopup)="closeEventDetailsPopup($event)"
  ></app-scheduler-detail-popup>
</mbsc-popup>

<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewInstructorTemplate"></ng-container>

<ng-template #viewInstructorTemplate>
  <div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
      <div class="d-flex align-items-center">
        <div>
          <div class="placeholder-name">
            <div>
              @if (showAccountManagerDetails) {
              {{ getInitials(accManagerDetails?.firstName, accManagerDetails?.lastName) | uppercase }}
              } @else {
              {{ getInitials(selectedStudentDetails?.firstName, selectedStudentDetails?.lastName) | uppercase }}
              }
            </div>
          </div>
        </div>
        <div>
          <div class="name">
            @if (showAccountManagerDetails) {
            {{ accManagerDetails?.firstName }} {{ accManagerDetails?.lastName }}
            } @else {
            {{ selectedStudentDetails?.firstName }}
            {{ selectedStudentDetails?.lastName }}
            }
            <mat-icon class="ms-2" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">{{ selectedStudentDetails?.isAccountManager ? 'manage_accounts' : 'school' }}</mat-icon>
          </div>
          @if (!showAccountManagerDetails) {
          <div class="instructor-details-wrapper">
            <div class="instructor-details">
              Age <span class="text-gray ms-1">{{ selectedStudentDetails?.age }} Years</span>
            </div>
            <div class="dot"></div>
            <div class="instructor-details">
              <span class="text-gray ms-1">{{ selectedStudentDetails?.isNoSubstitute ? 'No Substitute Allowed' : 'Substitute Allowed' }}</span>
            </div>
          </div>
          }
        </div>
      </div>
      <div class="action-btn-wrapper">
        <button
          mat-raised-button
          color="accent"
          class="mat-accent-btn back-btn"
          type="button"
          (click)="showAccountManagerDetails && !(this.selectedStudentDetails?.accountManagerUserType === accountManagerUserTypes.YOUR_CHILD && this.selectedStudentDetails?.isAccountManager) ? setShowAccountManagerDetails(false) : closeViewSideNavFun()"
        >
          {{ showAccountManagerDetails ? 'Back' : 'Close' }}
        </button>
        @if (!isFromAttendance) {
          @if (!showAccountManagerDetails) {
            <button
              *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
              mat-raised-button
              color="primary"
              class="mat-primary-btn back-btn"
              type="button"
              (click)="onBookIntroductoryLesson()"
            >
              + Introductory
            </button>
            <button
              *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
              mat-raised-button
              color="primary"
              class="mat-primary-btn"
              type="button"
              (click)="openAssignPlanAndProduct()"
            >
              Plans & Products
            </button>
          }
        <button
          *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
          mat-raised-button
          color="primary"
          class="mat-primary-btn ms-1"
          type="button"
          (click)="isPaymentMethodSideNavOpen = true"
        >
          Payments
        </button>
        }
      </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
      <ng-container [ngTemplateOutlet]="showAccountManagerDetails ? managerDetailsTemp : studentDetailsTemp"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #managerDetailsTemp>
  <div class="row">
    <div class="col-md-7">
      <div class="student-detail-wrapper mt-3">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.files" alt="" />
            <div class="main-title">Customer Details</div>
            <span class="ps-2" *ngIf="accManagerDetails?.userType === userTypes.YOUR_CHILD">(Not a Customer)</span>
          </div>
          <div class="pointer student-content" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="primary-color" (click)="isEditAccountManagerSideNavOpen = true">
              <img [src]="constants.staticImages.icons.editPenGreen" alt="" />
            </div>
          </div>
        </div>
        <div>
          <div class="student-info-wrapper">
            <div class="student-content">
              <img [src]="constants.staticImages.icons.profileCircle" alt="" />
              <div class="sub-title">Name</div>
            </div>
            <div class="student-content-info">
              {{ accManagerDetails?.firstName }} {{ accManagerDetails?.lastName }}
            </div>
          </div>
          <div class="student-info-wrapper">
            <div class="student-content">
              <img [src]="constants.staticImages.icons.email" alt="" />
              <div class="sub-title">Email</div>
            </div>
            <div class="student-content-info">{{ accManagerDetails?.emailAddress | dashIfEmpty }}</div>
          </div>
          <div class="student-info-wrapper">
            <div class="student-content">
              <img [src]="constants.staticImages.icons.phone" alt="" />
              <div class="sub-title">Phone Number</div>
            </div>
            <div class="student-content-info">
              {{ accManagerDetails?.phoneNumber | dashIfEmpty }}
            </div>
          </div>
        </div>
      </div>

      @if (accManagerDetails?.dependentDetails?.length) {
      <div class="student-detail-wrapper mt-3">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.memberIcon" alt="" />
            <div class="main-title">Dependent Details</div>
          </div>
          <div class="pointer student-content" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="primary-color" (click)="isAddDependentSideNavOpen = true">Add dependent</div>
          </div>
        </div>

        <div>
          <ng-container *ngFor="let dependentInfo of accManagerDetails?.dependentDetails">
            <div class="dependent-main-wrapper">
              <div class="placeholder-name">
                <div>
                  {{ getInitials(dependentInfo.firstName, dependentInfo.lastName) | uppercase }}
                </div>
              </div>
              <div class="dependent-content-wrapper">
                <div class="dependent-name">{{ dependentInfo.firstName }} {{ dependentInfo.lastName }}</div>
                <div class="dependent-basic-detail-wrapper">
                  <div class="dependent-info">
                    <span class="title">Age:</span> <span class="text-gray">{{ getStudentAge(dependentInfo.dateOfBirth) }}</span>
                  </div>
                  <div class="dot"></div>
                  <div class="dependent-info primary-color pointer" (click)="onClickViewDependentDetails(dependentInfo.id)">
                    View details
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
      }
    </div>
  </div>
</ng-template>

<ng-template #studentDetailsTemp>
  <div class="row">
    <div class="col-md-7">
      <div class="student-detail-wrapper mb-3">
        <app-members-notes
          [studentId]="selectedStudentDetails!.id"
          (refreshNotes)="getStudentNotes($event)"
        ></app-members-notes>
      </div>
      <div class="student-detail-wrapper mb-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.message" alt="" />
            <div class="main-title">Messages</div>
          </div>
          <div class="primary-color pointer" (click)="toggleMessageSideNav(true)">View All</div>
        </div>
        @if (chatHistory && chatHistory.length) {
        <div class="student-info-wrapper mt-3 message">
          <ng-container [ngTemplateOutlet]="showMessageLoader ? showLoader : messageTemp"></ng-container>
        </div>
        } @else {
        <div class="no-schedule-available">No Messages Available</div>
        }
      </div>
      <ng-container [ngTemplateOutlet]="currentUser?.userRoleId === constants.roleIds.ADMIN || currentUser?.userRoleId === constants.roleIds.DESK_MANAGER ? scheduleTemp : null"></ng-container>
      @if (expiringPlans && expiringPlans.length) {
        <div class="student-detail-wrapper mt-3">
          <div class="student-header-wrapper">
            <div class="student-header-content">
              <img [src]="constants.staticImages.icons.checkedFile" alt="" />
              <div class="main-title">Expiring Plan</div>
            </div>
          </div>
          <div class="plan-details">
            @for (expiringPlan of expiringPlans; track $index) {
              <div class="plan-info-wrapper">
                <div class="plan-info">
                  <div class="fw-bold">
                    <ng-container *ngTemplateOutlet="planTypeTemplate; context: { planData: expiringPlan }"></ng-container>
                  </div>
                  <div class="student-content">
                    <div class="name">{{ expiringPlan.isEnsembleAvailable ? 'Ensemble Class' : expiringPlan.instrumentName }}</div>
                    <div class="dot"></div>
                    @if (!expiringPlan.isRentalPlan && !expiringPlan.isDDDPlan) {
                      <div class="text-gray">{{ planSummaryService.getTotalVisits(expiringPlan.planDetails) }} visits per week</div>
                      <div class="dot"></div>
                    }
                    <div class="text-gray">{{ expiringPlan.plan === plans.RECURRING ? 'Recurring' : 'Custom' }} plan</div>
                  </div>
                </div>
                <div class="plan-cancel-btn" (click)="openPlanRenewalSideNav(expiringPlan)" matTooltip="Renew Plan">
                  <img [src]="constants.staticImages.icons.arrowsCircle" alt="" class="arrow-circle">
                </div>
              </div>
              <div class="dotted-divider" *ngIf="!$last"></div>
            }
          </div>
        </div>
      }
      <div class="student-detail-wrapper mt-3">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.checkedFile" alt="" />
            <div class="main-title">Past Visits</div>
          </div>
          <div class="pointer student-content" (click)="isPastVisitSideNavOpen = true">
            <div class="primary-color">View All</div>
          </div>
        </div>
        <div class="student-header-wrapper">
          <div class="tab-item-content">
            @for (attendanceType of attendanceTypes | enumToKeyValue; track $index) {
              <div
                [ngClass]="{ item: true, 'active-item': selectedTabOption === attendanceType.value }"
                (click)="setActiveTabOption(attendanceType.value)">
                {{ attendanceType.key.replace('_', ' ') | titlecase }}
              </div>
            }
          </div>
        </div>
        <ng-container [ngTemplateOutlet]="showAttendanceLoader ? showLoader : studentAttendanceList"></ng-container>
      </div>
      <div class="student-detail-wrapper mt-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.checkedFile" alt="" />
            <div class="main-title">Active Passes</div>
          </div>
        </div>
        <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : studentPassTemp"></ng-container>
      </div>
    </div>
    <div class="col-md-5">
      <div class="student-detail-wrapper">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.trophy" alt="" />
            <div class="main-title">{{ isUpdateGrade ? 'Give Grade to Student' : 'Grade' }}</div>
          </div>
          <div
            class="pointer student-content"
            (click)="isUpdateGrade = true"
          >
            <div *ngIf="!isUpdateGrade" class="primary-color">Update Grade</div>
          </div>
        </div>
        <ng-container [ngTemplateOutlet]="isUpdateGrade ? updateGrade : studentGradeInfoTemplate"></ng-container>
      </div>

      <div
        class="student-detail-wrapper mt-3"
        *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR]"
      >
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.files" alt="" />
            <div class="main-title">Instructor Details</div>
          </div>
          @if (selectedStudentDetails?.instructorsDetails?.length! > constants.maxTwoVisibileItems) {
          <div class="pointer student-content">
            <div class="primary-color" (click)="showAllInstructor = !showAllInstructor">
              {{ showAllInstructor ? 'Show Less' : 'Show All' }}
            </div>
          </div>
          }
        </div>

        <div>
          @for (instructor of selectedStudentDetails?.instructorsDetails; track $index) {
            @if ($index < constants.maxTwoVisibileItems || showAllInstructor) {
              <div *ngIf="selectedStudentDetails?.instructorsDetails?.length! > 1" class="instructor-info">Instructor {{ $index + 1 }}</div>
              <div class="student-info-wrapper">
                <div class="student-content">
                  <img [src]="constants.staticImages.icons.profileCircle" alt="" />
                  <div class="sub-title">Name</div>
                </div>
                <div class="student-content-info pointer" (click)="toggleSideNavForInstructor(true, instructor.id)">
                  {{ instructor.name }}
                </div>
              </div>
              <div class="student-info-wrapper">
                <div class="student-content">
                  <img [src]="constants.staticImages.icons.email" alt="" />
                  <div class="sub-title">Email</div>
                </div>
                <div class="student-content-info">{{ instructor.email | dashIfEmpty }}</div>
              </div>
              <div *ngIf="shouldShowDivider(selectedStudentDetails?.instructorsDetails!.length, $index, showAllInstructor, constants.maxTwoVisibileItems)" class="dotted-divider"></div>
            }
         }
        </div>
      </div>
      <div class="student-detail-wrapper mt-3">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.files" alt="" />
            <div class="main-title">Account Manager</div>
          </div>
          <div class="pointer student-content">
            @if (!isFromAttendance) {
              <div class="primary-color" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]" (click)="setShowAccountManagerDetails(true)">View all</div>
            }
          </div>
        </div>
        <div>
          <div class="student-info-wrapper">
            <div class="student-content">
              <img [src]="constants.staticImages.icons.profileCircle" alt="" />
              <div class="sub-title">Name</div>
            </div>
            <div class="student-content-info">
              {{ accManagerDetails?.firstName }} {{ accManagerDetails?.lastName }}
            </div>
          </div>
          <ng-container *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="student-info-wrapper">
              <div class="student-content">
                <img [src]="constants.staticImages.icons.email" alt="" />
                <div class="sub-title">Email</div>
              </div>
              <div class="student-content-info">{{ accManagerDetails?.emailAddress | dashIfEmpty }}</div>
            </div>
            <div class="student-info-wrapper">
              <div class="student-content">
                <img [src]="constants.staticImages.icons.phone" alt="" />
                <div class="sub-title">Phone Number</div>
              </div>
              <div class="student-content-info">
                {{ accManagerDetails?.phoneNumber | dashIfEmpty }}
              </div>
            </div>
          </ng-container>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="currentUser?.userRoleId === constants.roleIds.INSTRUCTOR || currentUser?.userRoleId === constants.roleIds.SUPERVISOR ? scheduleTemp : null"></ng-container>
      @if (studentDocuments && studentDocuments.length) {
      <div class="student-detail-wrapper mt-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.fileCheckBottom" alt="" />
            <div class="main-title">Documents</div>
          </div>
          <div class="pointer student-content">
            <div class="primary-color" (click)="isViewDocumentSideNavOpen = true">View all</div>
          </div>
        </div>
        <div>
          <div class="student-info-wrapper documents-wrapper">
            @if (mainDocuments.length) {
              <div class="student-content">
                <div class="sub-title-doc">Enrollment Agreements</div>
              </div>
              @for (document of mainDocuments; track $index) {
              <div class="student-content-doc ms-4">
                <li>{{ document.signedDocuments.documentName }}</li>
              </div>
              }
            }
            @if (rentalDocuments.length) {
              <div class="student-content">
                <div class="sub-title-doc">Instrument Rental Agreements</div>
              </div>
              @for (document of rentalDocuments; track $index) {
              <div class="student-content-doc ms-4">
                <li>{{ document.signedDocuments.documentName }}</li>
              </div>
              }
            }
            @if (!mainDocuments.length && !rentalDocuments.length) {
              <div class="student-content-doc">
                <div class="sub-title-doc">No Documents Found</div>
              </div>
            }
          </div>
        </div>
      </div>
      }
      <div class="student-detail-wrapper mt-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.checkedFile" alt="" />
            <div class="main-title">Ensemble Plan</div>
          </div>
        </div>
        <div *ngIf="ensemblePlans && ensemblePlans?.length">
          @for (plan of ensemblePlans; track $index) {
            @if (plan.isEnsembleAvailable) {
              <div class="plan-details">
                <div class="plan-info-wrapper">
                  <div class="plan-info">
                    <div class="fw-bold content-info">
                      Weekly Music Lessons ({{ planSummaryService.getPlanType(plan.planType) }}-{{
                        planSummaryService.getPlanSummary(plan.planDetails)
                      }})
                    </div>
                    <div class="student-content">
                      <div class="text-gray content-info">{{ planSummaryService.getTotalVisits(plan.planDetails) }} visits per week</div>
                      <div class="dot"></div>
                      <div class="text-gray content-info">{{ plan.plan === plans.RECURRING ? 'Recurring' : 'Custom' }} plan</div>
                    </div>
                  </div>
                  <div class="plan-cancel-btn" (click)="openCancelPlanRequest(plan.studentplan.id ?? 0)" matTooltip="Cancel Plan">
                    <img [src]="constants.staticImages.icons.crossCircle" alt="" class="cross-circle">
                  </div>
                </div>
              </div>
            }
          }
        </div>
        <div *ngIf="ensemblePlans && !ensemblePlans.length">
          <div class="fw-semibold">No Ensemble Plan</div>
        </div>
      </div>
      <div class="student-detail-wrapper mt-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.checkedFile" alt="" />
            <div class="main-title">Active Plans</div>
          </div>
          <!-- To be used -->
          <!-- <div class="pointer student-content">
            <div class="primary-color">Change Plan</div>
          </div> -->
        </div>
        <ng-container [ngTemplateOutlet]="showStudentPlanLoader ? showLoader : studentPlanTemp"></ng-container>
      </div>
      <div class="student-detail-wrapper mt-3" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="student-header-wrapper">
          <div class="student-header-content">
            <img [src]="constants.staticImages.icons.payment" alt="" />
            <div class="main-title">Billing</div>
          </div>
          <div class="pointer student-content">
            <div class="primary-color" (click)="navigateToBillHistory()">View All</div>
          </div>
        </div>
        <div class="plan-details" *ngIf="billHistoryDetails && billHistoryDetails?.length">
          @for (billHistoryDetail of billHistoryDetails; track $index) {
            <div class="plan-info-wrapper" *ngIf="billHistoryDetail.userBillingTransactions">
              <div class="plan-info">
                <div class="fw-bold">
                  <ng-container [ngTemplateOutlet]="scheduleName" [ngTemplateOutletContext]="{ billHistory: billHistoryDetail }"></ng-container>
                </div>
                <div class="student-content fs-7">
                  <div class="name">{{ billHistoryDetail.userBillingTransactions.billPaymentDate | date: 'mediumDate' }}</div>
                  <div class="dot"></div>
                  <div class="text-gray">#{{ billHistoryDetail.userBillingTransactions.transactionId }}</div>
                </div>
              </div>
              <div class="primary-color fs-6 fw-bold">
                {{ billHistoryDetail.userBillingTransactions.billAmount | currency: 'USD' : 'symbol' : '1.2-2' }}
              </div>
            </div>
            <div *ngIf="!$last" class="dotted-divider"></div>
          }
        </div>
        <div *ngIf="!billHistoryDetails?.length">
          <div class="fw-semibold">No Billing History</div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #studentSchedule>
  @for (schedule of dependentSchedule; track $index) {
      <div class="student-schedule" [ngClass]="{'ensemble-schedule': schedule.classType === classTypes.ENSEMBLE_CLASS, 'past-schedule': schedulerService.isPastDate(schedule.start | localDate)}">
        <div class="student-content mb-1">
          <div class="schedule-date">{{ schedule.start | localDate | date : constants.fullDate }}</div>
          <div class="fw-bold">
            {{ schedule.start | localDate | date : constants.dateFormats.hh_mm_a }} -
            {{ schedule.end | localDate | date : constants.dateFormats.hh_mm_a }}
          </div>
          <div class="pointer ms-2" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <img [src]="constants.staticImages.icons.editPenGray" alt="" class="edit-icon" (click)="openEventDetailsPopup(schedule, $event)" />
          </div>
        </div>
        <div class="student-content mb-1 justify-content-between">
          <div [ngClass]="{ strike: schedule.isCanceled, 'd-flex align-items-center': schedule.classType === classTypes.ENSEMBLE_CLASS }">
            <span *ngIf="schedule.isCanceled">Canceled: </span>
            <span *ngIf="schedule.isDraftSchedule">Draft: </span>
            @switch (schedule.classType) { @case (classTypes.GROUP_CLASS) {
            <span>{{ schedule?.groupClassName | titlecase }}</span>}
            @case (classTypes.ENSEMBLE_CLASS) {
              <span>{{ schedule?.ensembleClassName | titlecase }}</span>
            } @case (classTypes.SUMMER_CAMP) {
            <span>{{ schedule?.campName | titlecase }}</span>
            } @case (classTypes.MAKE_UP) {
            <span>{{ schedule?.instrumentName }} Make-Up Lesson</span>
            } @case (classTypes.RECURRING) {
            <span>{{ schedule?.planName }}</span>
            } @case (classTypes.INTRODUCTORY) {
            <span>Introductory {{ schedule?.instrumentName }} Lesson</span>
            } @default {
            <span>{{ schedule?.instrumentName }} Lesson</span>
            } }
            <mat-icon class="ms-2" *ngIf="schedule.classType === classTypes.ENSEMBLE_CLASS">groups</mat-icon>
          </div>
          @if (schedule?.classType === classTypes.INTRODUCTORY && !schedule?.isPaid) {
            <button
              *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
              mat-raised-button
              color="primary"
              class="mat-primary-btn action-btn"
              type="button"
              (click)="onInitiatePaymentForIntroductoryClass(true, schedule)"
            >
              Pay
            </button>
          }
        </div>
        <div class="student-content">
          <div class="student-content">
            <img [src]="constants.staticImages.icons.location" class="black-img" alt="" />
            <div class="location">{{ schedule.locationName }}</div>
          </div>
          <div class="dot"></div>
          <div class="student-content">
            <img [src]="constants.staticImages.icons.profileCircle" class="black-img" alt="" />

            @if (schedule.classType === classTypes.ENSEMBLE_CLASS) {
              @for (assignedInstructors of schedule.assignedInstructors; track $index) {
              <ng-container *ngIf="$index < 1">
                 <span class="pointer" (click)="toggleSideNavForInstructor(true, assignedInstructors.instructorId)">
                  {{ assignedInstructors?.instructorName }}
                 </span> 
                <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div>
              </ng-container>
              } @if (schedule.assignedInstructors!.length>1) {
              <div class="remaining-instrument-available-count"
              [matTooltip]="getInstructorNames(schedule.assignedInstructors)">
                {{ schedule.assignedInstructors!.length - 1}}+
              </div>
              } } @else{
               <span (click)="toggleSideNavForInstructor(true, schedule.instructorId)" class="pointer">
                {{ schedule.instructorName }}
               </span> 
              }
          </div>
        </div>
      </div>

  <div *ngIf="!$last" class="dotted-divider"></div>
  }
</ng-template>

<ng-template #messageTemp>
  @for (chat of chatHistory; track $index) {
    <div class="chat-message" [ngClass]="{'mb-3': !$last}">
      <div class="chat-image">
        @if (chat.chatImage) {
          <img [src]="chat.chatImage" class="img me-3" alt="" />
          } @else {
          <div class="placeholder-name">
            <div>
              {{ getInitialsFromFullName(chat.chatName) | uppercase }}
            </div>
          </div>
          }
      </div>
      <div class="chat-details">
        <div class="chat-header">
          <div class="chat-name" [matTooltip]="chat.chatName">{{ chat.chatName | titlecase }}</div>
          <div class="chat-type" [ngClass]="{'public': chat.chatType === chatTypes.PUBLIC, 'private': chat.chatType === chatTypes.PRIVATE}">
            @if(chat.chatType === chatTypes.PUBLIC) {
              <img [src]="constants.staticImages.icons.openLock" class="lock" alt=""> Public
            }
            @else if (chat.chatType === chatTypes.PRIVATE) {
              <img [src]="constants.staticImages.icons.lock" class="lock" alt=""> Private
            }
          </div>
        </div>
        <div class="chat-message-text">
          @if(chat.isLastMessageDeleted) {
            <div class="notes">This message was deleted.</div>
          }
          @else {
            @if(chat.lastMessage) {
            <div class="notes" [matTooltip]="chat.lastMessage">{{ chat.lastMessage }}</div>
            }
            @else {
              @switch (chat.lastMessageFile.fileType) {
                @case (fileTypes.IMAGE) {
                <div class="notes">Sent an image</div>
                }
                @case (fileTypes.VIDEO) {
                <div class="notes">Sent a video</div>
                }
                @default {
                <div class="notes">Sent a file</div>
                }
              }
            }
          }
        </div>
        <div class="text-gray date">{{ chat.lastMessageTime | localDate | date: constants.fullDate }} at {{ chat.lastMessageTime | localDate | date: 'shortTime' }}</div>
      </div>
    </div>
  }
</ng-template>

<ng-template #updateGrade>
  <form [formGroup]="gradeFormGroup">
    <mat-form-field>
      <mat-label>Select Instrument</mat-label>
      <mat-select formControlName="instrumentId">
        <mat-option *ngFor="let instrument of instruments" [value]="instrument.instrumentDetail.id">
          {{ instrument.instrumentDetail.name }}
        </mat-option>
      </mat-select>
      <mat-error>
        <app-error-messages [control]="gradeFormGroup.controls.instrumentId"></app-error-messages>
      </mat-error>
    </mat-form-field>
    <mat-form-field>
      <mat-label>Select Grade</mat-label>
      <mat-select formControlName="grade">
        <mat-option *ngFor="let gradeLevel of gradeLevels" [value]="gradeLevel.gradeLevel.id">
          {{ gradeLevel.gradeLevel.name }}
        </mat-option>
      </mat-select>
      <mat-error>
        <app-error-messages [control]="gradeFormGroup.controls.grade"></app-error-messages>
      </mat-error>
    </mat-form-field>
    <div class="action-btn-wrapper text-end">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="resetGradeForm()">Cancel</button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn ms-2"
        type="button"
        (click)="onUpdateGrade()"
        [appLoader]="showBtnLoader"
      >
        Save
      </button>
    </div>
  </form>
</ng-template>

<ng-template #studentGradeInfoTemplate>
  <ng-container [ngTemplateOutlet]="showGradeLoader ? showLoader : studentGradeInfo"></ng-container>
</ng-template>

<ng-template #studentGradeInfo>
  <div *ngIf="studentGrades?.length">
    @for (grade of studentGrades; track $index) {
    <div class="student-grade-wrapper">
      <div class="student-grade-instrument">{{ grade.studentGrade.instrumentName | dashIfEmpty }}</div>
      <div class="student-grade-info">
        <div>
          Grade
          <span class="primary-color">{{ grade.studentGrade.grade | dashIfEmpty }} <mat-icon>arrow_upward</mat-icon> </span>
        </div>
        <div class="grade-updated">
          Updated On <span class="text-black">{{ grade.studentGrade.updatedOn | localDate | date }}</span>
        </div>
      </div>
    </div>
    }
  </div>
  <div *ngIf="!studentGrades.length">
    <div class="fw-semibold">No Grade Available</div>
  </div>
</ng-template>

<ng-template #studentPlanTemp>
  <div class="plan-details" *ngIf="otherPlans?.length">
    @for (plan of otherPlans; track $index) {
      <div class="plan-info-wrapper">
        <div class="plan-info">
          <div class="fw-bold">
            <ng-container *ngTemplateOutlet="planTypeTemplate; context: { planData: plan }"></ng-container>
          </div>
          <div class="student-content">
            @if (!plan.isRentalPlan && !plan.isDDDPlan) {
              <div class="name">{{ plan.isEnsembleAvailable ? 'Ensemble Class' : plan.instrumentName }}</div>
              <div class="dot"></div>
              <div class="text-gray">{{ planSummaryService.getTotalVisits(plan.planDetails) }} visits per week</div>
              <div class="dot"></div>
            }
            <div class="text-gray">{{ plan.plan === plans.RECURRING ? 'Recurring' : 'Custom' }} plan</div>
          </div>
        </div>
        <div class="plan-cancel-btn" (click)="openCancelPlanRequest(plan.studentplan.id ?? 0)" matTooltip="Cancel Plan">
          <img [src]="constants.staticImages.icons.crossCircle" alt="" class="cross-circle">
        </div>
      </div>
      <div *ngIf="!$last" class="dotted-divider"></div>
    }
  </div>
  <div *ngIf="!otherPlans.length">
    <div class="fw-semibold">No Assigned Plan</div>
  </div>
</ng-template>

<ng-template #studentPassTemp>
  <div class="plan-details" *ngIf="studentPasses?.length">
    @for (studentPass of studentPasses; track $index) {
        <div class="plan-info-wrapper">
          <div class="plan-info">
            <div class="fw-bold">
              {{ studentPass.passName }} ({{ studentPass.duration }})
            </div>
            <div class="student-content">
              <div class="text-gray">{{ studentPass.visits }} visit</div>
              <div class="dot"></div>
              <div class="text-gray">Expires on {{ studentPass.expiryDate | date }}</div>
            </div>
          </div>
          <div class="student-content">
            <button
                *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
                mat-raised-button
                color="primary"
                class="mat-primary-btn"
                type="button"
                (click)="onScheduleMakeUpLesson(studentPass)"
              >
                Schedule
            </button>
          </div>
        </div>
        <div *ngIf="!$last" class="dotted-divider"></div>
    }
  </div>
  <div *ngIf="!studentPasses?.length">
    <div class="fw-semibold">No Active Passes</div>
  </div>
</ng-template>

<ng-template #studentAttendanceList>
  <div *ngIf="studentAttendance && studentAttendance.length" class="attendance-wrapper">
    @for (attendance of studentAttendance; track $index) {
        <div class="fw-bold content-info fs-16" [ngClass]="{ strike: attendance.studentAllAttendance.isCancelled }">
          @if (attendance.studentAllAttendance.isCancelled) {
            <span>Canceled: </span>
          }
          @switch (attendance.studentAllAttendance.classType) {
            @case (classTypes.ENSEMBLE_CLASS) {
              {{ attendance.studentAllAttendance.ensembleClassName | titlecase }} ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
            }
            @case (classTypes.GROUP_CLASS) {
              {{ attendance.studentAllAttendance.groupClassName | titlecase }} ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
            }
            @case (classTypes.SUMMER_CAMP) {
              {{ attendance.studentAllAttendance.campName | titlecase }} ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
            }
            @default {
              {{ attendance.studentAllAttendance.instrumentName }} Lesson ({{ getTimeDiff(attendance.studentAllAttendance.startTime, attendance.studentAllAttendance.endTime) }})
            }  
          }
          @switch (attendance.studentAllAttendance.isPresent) {
            @case (true) {
              <img [src]="constants.staticImages.icons.checkCircle" alt="" class="attendance-img" matTooltip="Complete" />
            }
            @case (false) {
              <img [src]="constants.staticImages.icons.noShowBadge" alt="" class="attendance-img no-show" matTooltip="No Show" />
            }
            @case (null) {
              <img [src]="constants.staticImages.icons.timeCircleClock" class="attendance-img incomplete" alt="" matTooltip="Incomplete" />
            }
          }
        </div>
        <div class="student-content">
          <div class="text-gray content-info">{{ attendance.studentAllAttendance.startTime | localDate | date : constants.dateFormats.MMM_d_y }}</div>
          <div class="dot"></div>
          <div class="text-gray content-info">{{ attendance.studentAllAttendance.startTime | localDate | date : constants.dateFormats.hh_mm_a }} - {{ attendance.studentAllAttendance.endTime | localDate | date : constants.dateFormats.hh_mm_a }}</div>
          <div class="dot"></div>
          <div class="text-gray content-info">{{ attendance.studentAllAttendance.locationName }}</div>
        </div>
        <div *ngIf="!$last" class="dotted-divider"></div>
    }
  </div>
  <div *ngIf="studentAttendance && !studentAttendance.length">
    <div class="no-schedule-available">No Attendance available</div>
  </div>
</ng-template>

<ng-template #scheduleTemp>
  <div class="student-detail-wrapper mt-3">
    <div class="student-header-wrapper mb-4">
      <div class="student-header-content">
        <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
        <div class="main-title">Schedule</div>
        <div class="toggle-btn-wrapper ms-4" *appHasPermission="[constants.roles.SUPERVISOR]">
          @for (scheduleOption of constants.scheduleOptions; track $index) {
          <div
            [ngClass]="{ active: showSupervisorSchedule === scheduleOption.value }"
            class="select-btn"
            (click)="updateScheduleOption(scheduleOption.value)"
          >
            {{ scheduleOption.label }}
          </div>
          }
        </div>
      </div>
    </div>
    <div class="student-header-wrapper">
      <div (click)="updateWeek(-1)"><mat-icon>keyboard_arrow_left</mat-icon></div>
      <div class="date">{{ filters.startDate | date : constants.fullDate }} - {{ filters.endDate | date : constants.fullDate }}</div>
      <div (click)="updateWeek(1)"><mat-icon>keyboard_arrow_right</mat-icon></div>
    </div>
    @if (dependentSchedule && dependentSchedule.length) {
      <div class="student-info-wrapper mt-3 schedule">
        <ng-container [ngTemplateOutlet]="showScheduleLoader ? showLoader : studentSchedule"></ng-container>
      </div>
    } @else {
      <div class="no-schedule-available">No Schedule Available</div>
    }
  </div>
</ng-template>

<ng-template #scheduleName let-billHistory="billHistory">
  @switch (billHistory.userBillingTransactions.classType) {
  @case (classTypes.INTRODUCTORY) {
  <div>
    Introductory {{ billHistory.userBillingTransactions.instrumentName | titlecase }} Lesson
  </div>
  }
  @case (classTypes.RECURRING) {
  <div>
    @if (billHistory.userBillingTransactions.planId) {
      <ng-container *ngTemplateOutlet="planTypeTemplate; context: { planData: billHistory.userBillingTransactions }"></ng-container>
    }
    @else {
      {{ billHistory.userBillingTransactions.billPaymentDate | date: constants.dateFormats.month }} Recurring Lesson Bill
    }
  </div>
  }
  @case (classTypes.GROUP_CLASS) {
  <div>
    {{ billHistory.userBillingTransactions.groupClassName | titlecase }}
  </div>
  }
  @case (classTypes.SUMMER_CAMP) {
  <div>
    {{ billHistory.userBillingTransactions.summerCampName | titlecase }}
  </div>
  }
  @case (classTypes.ENSEMBLE_CLASS) {
  <div>
    {{ billHistory.userBillingTransactions.ensembleClassName | titlecase }}
  </div>
  }
  }
</ng-template>

<ng-template #planTypeTemplate let-planData="planData">
  @switch (true) {
    @case (planData.isDDDPlan) {
      {{ planData.instrumentName }} DDD Plan ({{ planData?.planDetails?.[0]?.planDetail?.duration }})
    }
    @case (planData.isRentalPlan) {
      {{ planData.instrumentName }} Rental Plan {{ planData.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
    }
    @default {
      Weekly {{ planData.isEnsembleAvailable ? "Ensemble" : planData.instrumentName }} Lessons ({{ planSummaryService.getPlanType(planData.planType) }}-{{ planSummaryService.getPlanSummary(planData.planDetails) }})
    }
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
