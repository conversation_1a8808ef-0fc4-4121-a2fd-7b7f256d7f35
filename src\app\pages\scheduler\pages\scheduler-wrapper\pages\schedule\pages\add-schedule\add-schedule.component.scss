@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;

  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }

  .mat-error-position {
    position: relative;
  }

  .instructor-loader-wrapper {
    @include flex-content-align-center;
    margin-bottom: 20px;

    .instructor-loading {
      font-size: 14px;
      font-weight: 600;
    }
  
    .loader {
      margin-right: 8px;
      border-top: 2px solid $primary-color !important;
    }
  }
}

.field-with-mat-inputs {
  margin-bottom: 6px;
}

.suggested-time-wrapper {
  .btn-typed-option {
    margin-bottom: 8px;
    padding: 8px;
    min-width: 165px;
    text-align: center;
    .date {
      color: $gray-text;
    }

    .time {
      font-weight: 600;
    }
  }

  .active {
    .date {
      color: $primary-color !important;
    }
  }
}

::ng-deep {
  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }
  }

  .instructor-loader .content-loader {
    margin-top: 0px !important;
  }

  .mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text {
    opacity: 1 !important;
    width: 100%;

    .instructor-list {
      @include flex-content-space-between;

      .instructor-name {
        opacity: 0.38 !important;
      }
    }
  }

  .mat-mdc-option .mdc-list-item__primary-text {
    width: 100%;
  }

  .time {
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }

  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .o-sidebar-wrapper .o-sidebar-body {
    overflow: auto;
    height: calc(100vh - 68px);
    padding: 20px 30px 10px 30px !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-mdc-form-field-error-wrapper {
    padding: 0 !important;
  }

  .special-need {
    .mdc-label {
      font-size: 15px !important;
    }
  }

  .mat-start-date {
    .mat-mdc-text-field-wrapper,
    .mat-mdc-icon-button .mat-mdc-button-touch-target,
    .mat-mdc-button-ripple {
      height: 35px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      height: 35px;
      width: 35px;
    }

    .mat-mdc-icon-button .mat-mdc-button-ripple {
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button svg {
      height: 16px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      padding: 5px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      top: -9px;
    }

    .mat-datepicker-input {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
      border-radius: 9%;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before {
      background: transparent !important;
    }
  }

  .search-bar-wrapper-student {
    .mdc-text-field {
      background-color: $white-color !important;
      border: 1px solid $btn-options-border-color;
    }
  }

  .mdc-list-item__primary-text {
    @include w-h-100;
  }

  .plan-info-wrapper {
    @include flex-content-space-between;
    @include w-h-100;
    color: $gray-text;
    font-weight: 600;
  }

  .plan-info-content {
    .plan-price {
      color: $black-color !important;
      font-weight: 700;
    }
  }

  .select-plan-info-content {
    @include flex-content-space-between;
    @include w-h-100;
    margin-right: 2px;
  }
}

@media (max-width: 530px) {
  .o-sidebar-header,
  .field-wrapper {
    flex-wrap: wrap;
  }

  .single-btn-select-wrapper .select-btn {
    padding: 12px 9px;
  }
}
