@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.field-wrapper {
  .instructor-loader-wrapper {
    @include flex-content-align-center;
    margin-bottom: 20px;

    .instructor-loading {
      font-size: 14px;
      font-weight: 600;
    }

    .loader {
      margin-right: 8px;
      border-top: 2px solid $primary-color !important;
    }
  }
}

::ng-deep {
  .mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input {
    color: $black-shade-text !important;
    font-size: 14px;
  }

  .mdc-form-field > label {
    padding-left: 0px !important;
    color: $original-black-color !important;
  }

  .mat-mdc-radio-checked {
    .mdc-label {
      color: $primary-color !important;
    }
  }
}

.plan-info-content {
  background-color: $gray-bg-light !important;
  border-radius: 6px !important;
  font-size: 15px;
  padding: 8px 12px;
}
