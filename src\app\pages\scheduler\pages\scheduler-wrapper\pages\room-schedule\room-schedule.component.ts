import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { CBResponse, IdNameModel, MatDialogRes } from 'src/app/shared/models';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { AppToasterService, CommonService, NavigationService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { Instrument, InstrumentsDetail } from 'src/app/request-information/models';
import { RoomDetails, RoomInfo, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { MbscResource, MbscEventcalendarOptions, MbscCalendarEvent, MbscModule, MbscPopup } from '@mobiscroll/angular';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { POPUP_OPTIONS } from 'src/app/shared/constants';
import { MatButtonModule } from '@angular/material/button';
import { RoomScheduleService } from './services';
import { Debounce } from 'src/app/shared/decorators';
import { RoomSchedule } from './models';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';
import { ActivatedRoute, Router } from '@angular/router';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';
import { FilterItem } from '../schedule/models';
import { MatTooltipModule } from '@angular/material/tooltip';

const DEPENDENCIES = {
  MODULES: [
    MatIconModule,
    MatFormFieldModule,
    MatDatepickerModule,
    CommonModule,
    FormsModule,
    MatSelectModule,
    MatInputModule,
    MbscModule,
    SharedModule,
    MatButtonModule,
    MatTooltipModule
  ],
  COMPONENTS: [MultiSelectComponent]
};

@Component({
  selector: 'app-room-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './room-schedule.component.html',
  styleUrl: './room-schedule.component.scss'
})
export class RoomScheduleComponent extends BaseComponent implements OnInit {
  @ViewChild('roomDetailsPopup', { static: false }) roomDetailsPopup!: MbscPopup;
  @ViewChild('roomInfoPopup', { static: false }) roomInfoPopup!: MbscPopup;

  showScheduleForDate = new Date();
  firstDateOfCurrentWeek!: Date;
  lastDateOfCurrentWeek!: Date;
  isLoading!: boolean;

  detailsAnchor!: EventTarget | null;
  selectedEvent!: RoomSchedule | undefined;

  infoAnchor!: EventTarget | null;
  selectedRoomInfo!: RoomInfo | undefined;

  resources!: MbscResource[];
  schedulerData!: MbscCalendarEvent[];
  schoolLocations!: Array<SchoolLocations>;
  selectedLocation = 0;
  currentDateTime = this.datePipe
    .transform(this.showScheduleForDate, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss)
    ?.toString();

  popupOptions = { ...POPUP_OPTIONS, width: '350px' };

  @Output() selectedRoom = new EventEmitter<RoomSchedule>();

  filterParams = {
    instructor: {
      id: 2,
      defaultPlaceholder: 'All Instructors',
      placeholder: 'All Instructors',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instrument: {
      id: 3,
      placeholder: 'All Instruments',
      defaultPlaceholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    }
  };

  calendarSettings: MbscEventcalendarOptions = {
    view: {
      timeline: {
        type: 'week',
        eventList: true
      }
    }
  };
  allInstrumentSelected = false;

  constructor(
    private readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly roomService: RoomService,
    private readonly cdr: ChangeDetectorRef,
    private readonly roomScheduleService: RoomScheduleService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly instructorService: InstructorService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly navigationService: NavigationService,
    private readonly localStorageService: LocalStorageService
  ) {
    super();
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.setLocationFilter();
    this.getFilterData();
  }

  setLocationFilter(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.locationId) {
        this.selectedLocation = +params.locationId;
        this.removeQueryParams();
      }
    });
  }

  removeQueryParams(): void {
    this.router.navigate([], { queryParams: { locationId: null }, queryParamsHandling: 'merge' });
  }

  @Debounce(300)
  getScheduleData(minDate: Date, maxDate: Date): void {
    this.isLoading = true;
    this.roomScheduleService
      .add(this.getFilterParams(minDate, maxDate), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Array<MbscCalendarEvent>>) => {
          if (res.result) {
            this.schedulerData = res.result.items.map((event: MbscCalendarEvent) => {
              const roomSchedule = { ...event['roomSchedules'] };
              // Production fixes
              // roomSchedule.start = DateUtils.toLocal(roomSchedule.start);
              // roomSchedule.end = DateUtils.toLocal(roomSchedule.end);
              roomSchedule.scheduleStartDate = DateUtils.toLocal(roomSchedule.scheduleStartDate);
              roomSchedule.scheduleEndDate = DateUtils.toLocal(roomSchedule.scheduleEndDate);
              return roomSchedule;
            });
          }
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterData(): void {
    this.getLocations();
    this.getInstruments();
    this.getInstructors();
    this.applyFiltersToPersist();
  }

  applyFiltersToPersist(): void {
    const schedulerFilters = this.localStorageService.getItem(StorageItem.RoomSchedulerFilters);
    if (schedulerFilters) {
      this.filterParams.instructor.value = new Set(
        schedulerFilters.instructorIdFilter || this.filterParams.instructor.value
      );
      this.filterParams.instrument.value = new Set(
        schedulerFilters.instrumentIdFilter || this.filterParams.instrument.value
      );
      this.selectedLocation = schedulerFilters.locationId || this.selectedLocation;
    }
  }

  getFirstInstrumentOfInstructor(instrumentList: Array<InstrumentsDetail>): InstrumentsDetail {
    if (!instrumentList.length) {
      return { colorCode: '#FFE1F8', fontColorCode: '#FD35D1' } as InstrumentsDetail;
    }

    return instrumentList[0];
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          const { items } = res.result;
          this.filterParams.instrument.options = items.map((item) => ({
            id: item.instrumentDetail.id,
            name: item.instrumentDetail.name
          }));
          // TODO
          // this.filterParams.instrument.value = new Set(items.map((item) => item.instrumentDetail.id));
          this.filterParams.instrument.totalCount = items.length;
          // const schedulerFilters = this.localStorageService.getItem(StorageItem.RoomSchedulerFilters);
          // this.filterParams.instrument.value = schedulerFilters?.instrumentIdFilter
          //   ? new Set(schedulerFilters.instrumentIdFilter)
          //   : this.filterParams.instrument.value;
          this.setPlaceholder(this.filterParams.instrument);
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.schoolLocations = res.result.items;
          if (this.selectedLocation === 0) {
            this.selectedLocation = this.schoolLocations[0].schoolLocations.id;
          }
          const schedulerFilters = this.localStorageService.getItem(StorageItem.RoomSchedulerFilters);
          this.selectedLocation = schedulerFilters?.locationId || this.selectedLocation;
          this.showScheduleForDate = schedulerFilters?.minScheduleDate
            ? new Date(schedulerFilters.minScheduleDate)
            : new Date();
          this.setPlaceholder(this.filterParams.instructor);
          this.setFirstAndLastDateOfCurrentWeek(this.showScheduleForDate);
          this.getRooms();
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForInstructors() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: SupervisorFilter.ALL,
      Page: 1
    });
  }

  getInstructors(): void {
    this.instructorService
      .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorList>) => {
          const items = res.result.items;
          this.filterParams.instructor.options = items.map((item) => ({
            ...item.instructorDetail
          }));

          this.filterParams.instructor.totalCount = items.length;
          this.filterParams.instructor.value = new Set(this.filterParams.instructor.options.map((option) => option.id));
          const schedulerFilters = this.localStorageService.getItem(StorageItem.RoomSchedulerFilters);
          this.filterParams.instructor.value = schedulerFilters?.instructorIdFilter.length
            ? new Set(schedulerFilters.instructorIdFilter)
            : this.filterParams.instructor.value;
          this.setPlaceholder(this.filterParams.instructor);
          this.cdr.detectChanges();
        }
      });
  }

  setPlaceholder(filter: FilterItem): void {
    const selectedValues = Array.from(filter.value);
    const firstSelectedValueName = filter.options.find((option) => option.id === selectedValues[0])?.name;
    const additionalCount = selectedValues.length > 1 ? `+${selectedValues.length - 1}` : '';
    if (!selectedValues.length || selectedValues.length === filter.totalCount) {
      filter.placeholder = filter.defaultPlaceholder;
      return;
    }
    filter.placeholder = ` ${firstSelectedValueName} ${additionalCount}`;
  }

  getFilterParamsForRooms() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      page: 1,
      locationIdFilter: [this.selectedLocation]
    });
  }

  getRooms(): void {
    this.roomService
      .add(this.getFilterParamsForRooms(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          const items = res.result.items;
          this.resources = items.map((item) => ({
            ...item.roomDetail
          }));
          this.cdr.detectChanges();
        }
      });
  }

  onDeleteRoomSchedule(id: number | undefined): void {
    this.roomDetailsPopup?.close();
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Room Schedule`,
        message: `Are you sure you want to delete this room schedule?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteRoomSchedule(id);
      }
    });
  }

  deleteRoomSchedule(id: number | undefined): void {
    if (id) {
      this.roomScheduleService
        .delete(id, API_URL.crud.delete)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.setDateToCurrentDate(false);
            this.toasterService.success(
              this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Room Schedule')
            );
            this.cdr.detectChanges();
          }
        });
    }
  }

  openEventDetailsPopup(roomDetails: RoomSchedule | undefined, event: MouseEvent): void {
    this.selectedEvent = roomDetails;
    this.detailsAnchor = event.currentTarget || event.target;
    this.roomDetailsPopup?.open();
  }

  openRoomInfoPopup(roomDetails: RoomInfo | undefined, event: MouseEvent): void {
    this.selectedRoomInfo = roomDetails;
    this.infoAnchor = event.currentTarget || event.target;
    this.roomInfoPopup?.open();
  }

  openInstructorDetails(instructorId: number): void {
    this.navigationService.navigateToInstructorDetail(instructorId);
  }

  getFilterParams(minDate: Date, maxDate: Date): any {
    const minScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(minDate).startUtc;
    const maxScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(maxDate).endUtc;
    const selectedInstrumentIds = Array.from(this.filterParams.instrument.value).length;

    const persistentFilters = {
      minScheduleDate: minScheduleDateFilter,
      maxScheduleDate: maxScheduleDateFilter,
      locationId: this.selectedLocation,
      instructorIdFilter: Array.from(this.filterParams.instructor.value),
      instrumentIdFilter:
        selectedInstrumentIds === this.filterParams.instrument.totalCount
          ? []
          : Array.from(this.filterParams.instrument.value)
    };
    this.localStorageService.setItem(StorageItem.RoomSchedulerFilters, persistentFilters);
    return {
      ...persistentFilters,
      locationId: [this.selectedLocation]
    };
  }

  goToPreviousDate(): void {
    const previousDate = new Date(this.showScheduleForDate);
    previousDate.setDate(this.showScheduleForDate.getDate() - 7);
    this.setFirstAndLastDateOfCurrentWeek(previousDate);
    this.showScheduleForDate = previousDate;
  }

  goToNextDate(): void {
    const nextDate = new Date(this.showScheduleForDate);
    nextDate.setDate(this.showScheduleForDate.getDate() + 7);
    this.setFirstAndLastDateOfCurrentWeek(nextDate);
    this.showScheduleForDate = nextDate;
  }

  onCalendarDateChange(date: Date): void {
    this.showScheduleForDate = date;
    this.setDateToCurrentDate(false);
  }

  setDateToCurrentDate(currentDate = true): void {
    this.isLoading = true;
    this.showScheduleForDate = currentDate ? new Date() : this.showScheduleForDate;
    this.setFirstAndLastDateOfCurrentWeek(this.showScheduleForDate);
  }

  setFirstAndLastDateOfCurrentWeek(currentDate: Date): void {
    this.firstDateOfCurrentWeek = this.getFirstDateOfCurrentWeek(currentDate);
    this.lastDateOfCurrentWeek = new Date(this.firstDateOfCurrentWeek);
    this.lastDateOfCurrentWeek.setDate(this.firstDateOfCurrentWeek.getDate() + 6);
    this.getScheduleData(this.firstDateOfCurrentWeek, this.lastDateOfCurrentWeek);
  }

  getFirstDateOfCurrentWeek(currentDate: Date): Date {
    const date = new Date(currentDate);
    const dayOfWeek = date.getDay();
    const diff = date.getDate() - dayOfWeek;
    date.setDate(diff);
    date.setHours(0, 0, 0, 0);
    return date;
  }

  navigateToEditSchedule(roomData?: RoomSchedule): void {
    this.roomDetailsPopup.close();
    this.selectedRoom.emit(roomData);
  }

  getLocationNameFromValue(value: number | undefined): string {
    return this.schoolLocations.find((name) => name.schoolLocations.id === value)?.schoolLocations.locationName || '';
  }

  asIsOrder() {
    return 1;
  }
}
