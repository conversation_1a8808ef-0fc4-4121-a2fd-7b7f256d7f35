import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Constants, ROUTER_PATHS } from '../constants';
import { LocalStorageService, StorageItem } from '../services/local-storage.service';
import { Account } from 'src/app/auth/models/user.model';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard {
  constructor(
    private readonly router: Router,
    private readonly localStorageService: LocalStorageService
  ) {}

  canActivate(activatedRouteSnapshot: ActivatedRouteSnapshot): boolean {
    try {
      const currentUser: Account = this.localStorageService.getItem(StorageItem.CurrentUser) as Account;
      const permittedRoles = activatedRouteSnapshot.data?.['roles'];

      if (!currentUser || !permittedRoles) {
        console.warn('RoleGuard: Missing user or roles data');
        setTimeout(() => {
          this.router.navigate([ROUTER_PATHS.dashboard.root]);
        }, 0);
        return false;
      }

      if (permittedRoles.includes(currentUser.userRole) || permittedRoles.includes(Constants.roles.ALL)) {
        return true;
      }

      // Use setTimeout to avoid iOS navigation issues
      setTimeout(() => {
        this.router.navigate([ROUTER_PATHS.dashboard.root]);
      }, 0);
      return false;
    } catch (error) {
      console.error('RoleGuard error:', error);
      // Navigate to dashboard on error
      setTimeout(() => {
        this.router.navigate([ROUTER_PATHS.dashboard.root]);
      }, 0);
      return false;
    }
  }
}
